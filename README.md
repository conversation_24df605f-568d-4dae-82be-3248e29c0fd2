# SOW Generator - AI-Powered Statement of Work Generator

A modern, intelligent Statement of Work (SOW) generator built with Next.js, featuring AI-powered document processing, perfect format preservation, and seamless DOCX handling.

## 🚀 Features

### Core Functionality
- **AI-Powered Processing**: Advanced Gemini AI integration for intelligent document transformation
- **Perfect Format Preservation**: 1:1 DOCX format preservation through all conversion stages
- **Apple Pages Compatibility**: Generated documents work seamlessly with Apple Pages
- **Smart Template Analysis**: Automatic detection of required fields and intelligent suggestions
- **Real-time Preview**: Live DOCX preview with format-preserving conversion

### User Experience
- **Modern UI/UX**: Beautiful, responsive design with glass-morphism effects
- **Intelligent Workflow**: Step-by-step guided process with smart auto-fill
- **Large Preview Windows**: Enhanced document visibility for better review
- **Reasonable Missing Info Detection**: Practical, user-friendly validation
- **Multi-format Support**: DOCX upload, markdown processing, and DOCX output

### Technical Excellence
- **Format Preservation**: Uses pandoc with reference documents for perfect formatting
- **Consistent DOCX Handling**: Unified viewer components across all pages
- **Robust Error Handling**: Comprehensive validation and fallback mechanisms
- **Performance Optimized**: Efficient file processing and memory management

## 🛠 Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **AI Integration**: Google Gemini API
- **Document Processing**: Pandoc, docx-preview
- **File Handling**: Multer, fs/promises
- **Styling**: Tailwind CSS with custom glass-morphism effects

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Pandoc (for document conversion)
- Google Gemini API key

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Acceler8-Digital-LLC/SOW-Gen.git
   cd SOW-Gen
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Pandoc**
   ```bash
   # macOS
   brew install pandoc

   # Ubuntu/Debian
   sudo apt-get install pandoc
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your API keys:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage

### Basic Workflow

1. **Upload Template**: Upload a DOCX template or select from existing templates
2. **AI Analysis**: Automatic template analysis and field detection
3. **Input Information**: Fill out the intelligent form with project details
4. **AI Processing**: Advanced AI transformation with format preservation
5. **Review & Edit**: Large preview window with missing info detection
6. **Final Generation**: Perfect DOCX output with Apple Pages compatibility

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is proprietary software owned by Acceler8 Digital LLC.

---

**Built with ❤️ by Acceler8 Digital LLC**
# Trigger Railway deployment.
