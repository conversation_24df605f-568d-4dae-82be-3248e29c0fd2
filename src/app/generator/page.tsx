'use client';

import React from 'react';
import Link from 'next/link';

const GeneratorSelectionPage = () => {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Floating Elements */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-blue-400/20 rounded-full animate-bounce delay-300"></div>
      <div className="absolute top-40 left-20 w-3 h-3 bg-purple-400/20 rounded-full animate-bounce delay-700"></div>
      <div className="absolute bottom-40 right-40 w-5 h-5 bg-pink-400/20 rounded-full animate-bounce delay-1000"></div>

      <div className="relative z-10 container mx-auto px-6 pt-32 pb-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-white mb-6">
            Document Generator
          </h1>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto">
            Professional document generation for your business needs.
          </p>
        </div>

        {/* Generator Options */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* SOW Generator Card */}
            <Link href="/sow-generator" className="group">
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 h-full transition-all duration-500 hover:bg-white/15 hover:border-white/30 hover:scale-105 hover:shadow-2xl">
                <div className="text-center">
                  {/* Icon */}
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>

                  {/* Title */}
                  <h2 className="text-3xl font-bold text-white mb-4 group-hover:text-blue-300 transition-colors duration-300">
                    Create New SOW
                  </h2>

                  {/* Description */}
                  <p className="text-slate-300 mb-6 text-lg leading-relaxed">
                    Generate professional Statements of Work from templates using AI-powered content generation and smart field detection.
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Upload DOCX templates
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      AI-powered template analysis
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Smart field completion
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Professional DOCX output
                    </div>
                  </div>

                  {/* CTA */}
                  <div className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold transition-all duration-300 group-hover:from-blue-700 group-hover:to-purple-700 group-hover:shadow-lg">
                    Start SOW Generator →
                  </div>
                </div>
              </div>
            </Link>

            {/* Addendum Generator Card */}
            <Link href="/addendum-generator" className="group">
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 h-full transition-all duration-500 hover:bg-white/15 hover:border-white/30 hover:scale-105 hover:shadow-2xl">
                <div className="text-center">
                  {/* Icon */}
                  <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>

                  {/* Title */}
                  <h2 className="text-3xl font-bold text-white mb-4 group-hover:text-green-300 transition-colors duration-300">
                    Create Addendum
                  </h2>

                  {/* Description */}
                  <p className="text-slate-300 mb-6 text-lg leading-relaxed">
                    Generate contract addendums by analyzing completed SOWs and addendum templates with intelligent cross-referencing.
                  </p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Dual-file upload system
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      SOW context extraction
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Intelligent pre-filling
                    </div>
                    <div className="flex items-center text-slate-300 text-sm">
                      <svg className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Legal cross-references
                    </div>
                  </div>

                  {/* CTA */}
                  <div className="px-8 py-4 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-xl font-bold transition-all duration-300 group-hover:from-green-700 group-hover:to-teal-700 group-hover:shadow-lg">
                    Start Addendum Generator →
                  </div>
                </div>
              </div>
            </Link>
          </div>


        </div>
      </div>
    </div>
  );
};

export default GeneratorSelectionPage;
