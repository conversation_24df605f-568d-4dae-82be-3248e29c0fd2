import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, readFile, unlink } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Convert DOCX to markdown using pandoc
async function convertDocxToMarkdown(docxPath: string, templateId?: string): Promise<{
  markdown: string;
  placeholders: string[];
}> {
  try {
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const tempMarkdownPath = join(uploadsDir, `temp-${Date.now()}.md`);

    // Use pandoc to convert DOCX to markdown with content control anchor preservation
    const pandocCommand = `pandoc "${docxPath}" -f docx -t markdown --wrap=none --preserve-tabs -o "${tempMarkdownPath}"`;

    console.log('Running pandoc command:', pandocCommand);
    await execAsync(pandocCommand);

    // Read the generated markdown
    const markdown = await readFile(tempMarkdownPath, 'utf-8');

    // DEBUG: Save markdown for inspection (keep the conversion for verification)
    if (templateId) {
      const debugMarkdownPath = join(uploadsDir, `${templateId}-original-conversion.md`);
      await writeFile(debugMarkdownPath, markdown, 'utf-8');
      console.log('DEBUG: Saved original markdown conversion to:', debugMarkdownPath);
    }
    console.log('Original markdown length:', markdown.length);
    console.log('First 500 chars:', markdown.substring(0, 500));

    // Clean up temporary file
    try {
      await unlink(tempMarkdownPath);
    } catch (error) {
      console.warn('Failed to clean up temporary markdown file:', error);
    }

    // Find placeholders in markdown using multiple patterns
    const placeholders = new Set<string>();

    // Common placeholder patterns - prioritize {FIELD} format for docx-templates
    const patterns = [
      /\{([A-Z_]+)\}/g,           // {CLIENT_NAME}, {PROJECT_NAME} - preferred format
      /\[([A-Z_]+)\]/g,           // [CLIENT_NAME], [PROJECT_NAME]
      /\$\{([A-Z_]+)\}/g,         // ${CLIENT_NAME}, ${PROJECT_NAME}
      /\{\{([A-Z_]+)\}\}/g,       // {{CLIENT_NAME}}, {{PROJECT_NAME}}
      /\{([a-z_]+)\}/g,           // {client_name}, {project_name}
      /\[([a-z_]+)\]/g,           // [client_name], [project_name]
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(markdown)) !== null) {
        placeholders.add(match[1]);
      }
    });

    // If no placeholders found, add some common ones for demo
    if (placeholders.size === 0) {
      placeholders.add('CLIENT_COMPANY');
      placeholders.add('PROJECT_NAME');
      placeholders.add('CLIENT_NAME');
      placeholders.add('CLIENT_EMAIL');
      placeholders.add('START_DATE');
      placeholders.add('END_DATE');
      placeholders.add('TOTAL_COST');
    }

    console.log('Found placeholders:', Array.from(placeholders));

    return {
      markdown,
      placeholders: Array.from(placeholders)
    };
  } catch (error) {
    console.error('DOCX conversion error:', error);
    throw new Error('Failed to convert DOCX to markdown: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

// Template interface
interface Template {
  id: string;
  name: string;
  originalMarkdown: string;
  placeholders: string[];
  uploadDate: string;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('template') as File;
    const saveAsTemplate = formData.get('saveAsTemplate') === 'true';

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    if (!file.name.toLowerCase().endsWith('.docx')) {
      return NextResponse.json({ error: 'Only DOCX files are supported' }, { status: 400 });
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return NextResponse.json({ error: 'File too large (max 10MB)' }, { status: 400 });
    }

    // Create upload directory
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Generate ID and save original file
    const templateId = uuidv4();
    const fileName = `${templateId}.docx`;
    const filePath = join(uploadsDir, fileName);

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Convert DOCX to markdown for AI processing (but keep original DOCX intact)
    const { markdown, placeholders } = await convertDocxToMarkdown(filePath, templateId);

    // Save markdown version for AI processing only
    const markdownPath = join(uploadsDir, `${templateId}.md`);
    await writeFile(markdownPath, markdown);

    // Save template metadata (minimal data, original DOCX is preserved)
    const templateData: Template = {
      id: templateId,
      name: file.name,
      originalMarkdown: markdown, // Keep for backward compatibility
      placeholders: placeholders,
      uploadDate: new Date().toISOString()
    };

    // Save metadata
    const metadataPath = join(uploadsDir, `${templateId}.json`);
    await writeFile(metadataPath, JSON.stringify(templateData, null, 2));

    // Only add to templates list if user chose to save as template
    if (saveAsTemplate) {
      const templatesListPath = join(uploadsDir, 'templates.json');
      try {
        let templatesList = [];
        try {
          const existingData = await readFile(templatesListPath, 'utf-8');
          templatesList = JSON.parse(existingData);
        } catch (error) {
          // File doesn't exist, start with empty array
        }

        // Add new template to list (keep only essential info for listing)
        const templateListItem = {
          id: templateId,
          name: file.name,
          uploadDate: templateData.uploadDate,
          fields: placeholders,
          fileSize: buffer.length
        };

        templatesList.push(templateListItem);

        // Save updated list
        await writeFile(templatesListPath, JSON.stringify(templatesList, null, 2));

        console.log('Template added to templates list:', templateListItem);
      } catch (error) {
        console.warn('Failed to update templates list:', error);
      }
    } else {
      console.log('Template uploaded for one-time use only (not saved to templates list)');
    }

    return NextResponse.json({
      id: templateId,
      name: file.name,
      markdown: markdown,
      fields: placeholders,
      savedAsTemplate: saveAsTemplate,
      message: saveAsTemplate
        ? 'Template uploaded and saved to your template library!'
        : 'Template uploaded for one-time use!'
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Upload failed: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
