import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface UploadedTemplate {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

// GET - Load list of uploaded templates
export async function GET(request: NextRequest) {
  try {
    console.log('📂 TEMPLATE LIST: Loading uploaded templates list...');

    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    console.log('📂 TEMPLATE LIST: Using uploads directory:', uploadsDir);
    console.log('🌍 TEMPLATE LIST: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');
    console.log('🔍 TEMPLATE LIST: Looking for templates list at:', templatesListPath);

    // Ensure uploads directory exists (Railway deployment fix)
    try {
      const { mkdir } = await import('fs/promises');
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
      console.log('📁 TEMPLATE LIST: Directory creation result:', error);
    }

    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      const templates: UploadedTemplate[] = JSON.parse(templatesData);

      console.log('✅ TEMPLATE LIST: Loaded uploaded templates:', templates.length);
      console.log('📋 TEMPLATE LIST: Template IDs:', templates.map(t => t.id));
      return NextResponse.json(templates);

    } catch (error) {
      // File doesn't exist or is empty, return empty array
      console.log('📝 TEMPLATE LIST: No uploaded templates found, returning empty list');
      console.log('⚠️ TEMPLATE LIST: Error details:', error);
      return NextResponse.json([]);
    }

  } catch (error) {
    console.error('❌ TEMPLATE LIST: Failed to load uploaded templates:', error);
    return NextResponse.json(
      { error: 'Failed to load uploaded templates', details: String(error) },
      { status: 500 }
    );
  }
}
