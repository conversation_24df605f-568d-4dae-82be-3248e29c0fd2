import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface UploadedTemplate {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

// GET - Load list of uploaded templates
export async function GET(request: NextRequest) {
  try {
    console.log('📂 Loading uploaded templates list...');
    
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');
    
    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      const templates: UploadedTemplate[] = JSON.parse(templatesData);
      
      console.log('✅ Loaded uploaded templates:', templates.length);
      return NextResponse.json(templates);
      
    } catch (error) {
      // File doesn't exist or is empty, return empty array
      console.log('📝 No uploaded templates found, returning empty list');
      return NextResponse.json([]);
    }
    
  } catch (error) {
    console.error('❌ Failed to load uploaded templates:', error);
    return NextResponse.json(
      { error: 'Failed to load uploaded templates', details: String(error) },
      { status: 500 }
    );
  }
}
