import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import <PERSON>z<PERSON><PERSON> from 'pizzip';

const execAsync = promisify(exec);

// REMOVED: Complex DOCX modification method that was causing conflicts
// Using simplified pandoc approach instead

// REMOVED: Complex helper functions that were causing conflicts
// Using simplified pandoc approach instead

// REMOVED: Complex improved pandoc conversion method
// Using simplified approach instead

// SIMPLIFIED PANDOC CONVERSION: Clean, reliable approach matching main branch
async function simplifiedPandocConversion(
  markdown: string,
  templateId: string
): Promise<Buffer> {
  try {
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    // Create temporary markdown file
    const tempMarkdownPath = join(uploadsDir, `temp-${Date.now()}.md`);
    await writeFile(tempMarkdownPath, markdown, 'utf-8');

    // Create output DOCX path
    const outputDocxPath = join(uploadsDir, `converted-${Date.now()}.docx`);

    // Build pandoc command - simplified and reliable
    let pandocCommand;

    try {
      // Check if reference document exists
      await readFile(originalDocxPath);
      pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${outputDocxPath}"`;
      console.log('✅ SIMPLIFIED CONVERSION: Using reference document for format preservation');
    } catch {
      console.warn('⚠️ SIMPLIFIED CONVERSION: Reference document not found, using basic conversion');
      pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx -o "${outputDocxPath}"`;
    }

    console.log('🔧 SIMPLIFIED CONVERSION: Running pandoc command:', pandocCommand);

    // Execute pandoc conversion
    await execAsync(pandocCommand);

    // Read the generated DOCX
    const outputBuffer = await readFile(outputDocxPath);
    console.log('📊 SIMPLIFIED CONVERSION: Generated DOCX size:', outputBuffer.length);

    // Clean up temporary files
    try {
      await unlink(tempMarkdownPath);
      await unlink(outputDocxPath);
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }

    console.log('✅ SIMPLIFIED CONVERSION: Successful');
    return outputBuffer;

  } catch (error) {
    console.error('❌ SIMPLIFIED CONVERSION: Failed:', error);
    throw new Error('Simplified pandoc conversion failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}



export async function POST(request: NextRequest) {
  try {
    const { templateId, markdown } = await request.json();

    if (!templateId || !markdown) {
      return NextResponse.json({ error: 'Missing template ID or markdown' }, { status: 400 });
    }

    console.log('🎯 SIMPLIFIED PANDOC CONVERSION - template ID:', templateId);
    console.log('Gemini-processed markdown length:', markdown.length);

    // SIMPLIFIED: Use single, reliable conversion method matching main branch
    const docxBuffer = await simplifiedPandocConversion(markdown, templateId);

    // Validate the generated DOCX buffer
    if (!docxBuffer || docxBuffer.length === 0) {
      throw new Error('Generated DOCX buffer is empty');
    }

    console.log('Final DOCX buffer size for download:', docxBuffer.length);

    // Return the file with proper headers
    return new NextResponse(docxBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="SOW-Generated-${new Date().toISOString().split('T')[0]}.docx"`,
        'Content-Length': docxBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Simplified pandoc conversion error:', error);
    return NextResponse.json(
      { error: 'Pandoc conversion failed: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
