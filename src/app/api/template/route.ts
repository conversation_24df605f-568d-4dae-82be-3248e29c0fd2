import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';

interface UploadedTemplate {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

// DELETE - Remove an uploaded template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      console.log('❌ TEMPLATE DELETE: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ TEMPLATE DELETE: Starting deletion of uploaded template:', templateId);

    // Use Railway storage path if available
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    console.log('📁 TEMPLATE DELETE: Using uploads directory:', uploadsDir);
    console.log('📄 TEMPLATE DELETE: Templates list path:', templatesListPath);

    // Ensure uploads directory exists
    try {
      const { mkdir } = await import('fs/promises');
      await mkdir(uploadsDir, { recursive: true });
      console.log('📁 TEMPLATE DELETE: Ensured uploads directory exists');
    } catch (error) {
      console.log('📁 TEMPLATE DELETE: Directory creation result:', error);
    }

    // Load existing templates list
    let templates: UploadedTemplate[] = [];
    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      templates = JSON.parse(templatesData);
      console.log('📂 TEMPLATE DELETE: Loaded templates list with', templates.length, 'templates');
    } catch (error) {
      console.log('📝 TEMPLATE DELETE: No templates list found or error reading:', error);
      return NextResponse.json(
        { error: 'Template not found - no templates list exists' },
        { status: 404 }
      );
    }

    // Find and remove the template from the list
    const templateToDelete = templates.find(t => t.id === templateId);
    const updatedTemplates = templates.filter(t => t.id !== templateId);

    if (updatedTemplates.length === templates.length) {
      console.log('❌ TEMPLATE DELETE: Template not found in list:', templateId);
      console.log('📋 TEMPLATE DELETE: Available template IDs:', templates.map(t => t.id));
      return NextResponse.json(
        { error: 'Template not found in templates list' },
        { status: 404 }
      );
    }

    console.log('🎯 TEMPLATE DELETE: Found template to delete:', templateToDelete);

    // Delete the actual files
    const filesToDelete = [
      join(uploadsDir, `${templateId}.docx`),
      join(uploadsDir, `${templateId}.json`),
      join(uploadsDir, `${templateId}.md`)
    ];

    let deletedFiles = 0;
    for (const filePath of filesToDelete) {
      try {
        await unlink(filePath);
        console.log('🗑️ TEMPLATE DELETE: Successfully deleted file:', filePath);
        deletedFiles++;
      } catch (error) {
        console.warn('⚠️ TEMPLATE DELETE: Could not delete file (may not exist):', filePath, error);
      }
    }

    console.log('📊 TEMPLATE DELETE: Deleted', deletedFiles, 'out of', filesToDelete.length, 'files');

    // Save updated templates list
    try {
      await writeFile(templatesListPath, JSON.stringify(updatedTemplates, null, 2));
      console.log('✅ TEMPLATE DELETE: Updated templates list saved. Remaining templates:', updatedTemplates.length);
    } catch (error) {
      console.error('❌ TEMPLATE DELETE: Failed to save updated templates list:', error);
      return NextResponse.json(
        { error: 'Failed to update templates list after deletion' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
      remainingCount: updatedTemplates.length,
      deletedFiles: deletedFiles
    });

  } catch (error) {
    console.error('❌ TEMPLATE DELETE: Failed to delete uploaded template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template', details: String(error) },
      { status: 500 }
    );
  }
}
