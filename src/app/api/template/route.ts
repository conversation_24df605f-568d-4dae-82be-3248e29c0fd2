import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';

interface UploadedTemplate {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

// DELETE - Remove an uploaded template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ Deleting uploaded template:', templateId);

    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    // Load existing templates list
    let templates: UploadedTemplate[] = [];
    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      templates = JSON.parse(templatesData);
    } catch (error) {
      console.log('📝 No templates list found');
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    console.log('📂 Found templates before deletion:', templates.length);

    // Find and remove the template from the list
    const updatedTemplates = templates.filter(t => t.id !== templateId);

    if (updatedTemplates.length === templates.length) {
      console.log('❌ Template not found in list:', templateId);
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Delete the actual files
    const filesToDelete = [
      join(uploadsDir, `${templateId}.docx`),
      join(uploadsDir, `${templateId}.json`),
      join(uploadsDir, `${templateId}.md`)
    ];

    for (const filePath of filesToDelete) {
      try {
        await unlink(filePath);
        console.log('🗑️ Deleted file:', filePath);
      } catch (error) {
        console.warn('⚠️ Could not delete file (may not exist):', filePath);
      }
    }

    // Save updated templates list
    await writeFile(templatesListPath, JSON.stringify(updatedTemplates, null, 2));
    console.log('✅ Template deleted successfully. Remaining templates:', updatedTemplates.length);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
      remainingCount: updatedTemplates.length
    });

  } catch (error) {
    console.error('❌ Failed to delete uploaded template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template', details: String(error) },
      { status: 500 }
    );
  }
}
