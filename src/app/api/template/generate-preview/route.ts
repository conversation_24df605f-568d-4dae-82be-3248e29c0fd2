import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Generate updated DOCX using Gemini-processed content directly (no data extraction)
async function generateUpdatedDocxPreview(
  templateId: string,
  processedMarkdown: string
): Promise<string> {
  try {
    console.log('Using Gemini-processed markdown directly with pandoc - no data extraction needed');
    console.log('Processed markdown length:', processedMarkdown.length);

    // Let pandoc convert the complete Gemini-processed markdown to DOCX using original as reference
    return await convertMarkdownToDocxWithReference(templateId, processedMarkdown);

  } catch (error) {
    console.error('DOCX generation and preview error:', error);
    throw new Error('Failed to generate updated DOCX preview: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

// Convert Gemini-processed markdown to DOCX using original as reference, then to HTML for preview
async function convertMarkdownToDocxWithReference(
  templateId: string,
  processedMarkdown: string
): Promise<string> {
  try {
    // FIXED: Use consistent storage path for Railway deployment
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);
    const tempMarkdownPath = join(uploadsDir, `temp-${templateId}-${Date.now()}.md`);
    const tempDocxPath = join(uploadsDir, `temp-${templateId}-${Date.now()}.docx`);

    console.log('Converting Gemini-processed markdown to DOCX using original as reference');
    console.log('Reference document path:', originalDocxPath);
    console.log('Processed markdown length:', processedMarkdown.length);

    // Write processed markdown to temp file
    await writeFile(tempMarkdownPath, processedMarkdown, 'utf-8');

    // Convert markdown to DOCX using original as reference document to preserve formatting and content control anchors
    const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${tempDocxPath}"`;

    await execAsync(pandocCommand);
    console.log('Pandoc conversion successful - Gemini content now in DOCX format');

    // Read the generated DOCX
    const docxBuffer = await readFile(tempDocxPath);

    // Convert DOCX to HTML using pandoc for perfect 1:1 preservation
    const tempHtmlPath = join(uploadsDir, `temp-${templateId}-${Date.now()}.html`);
    const pandocHtmlCommand = `pandoc "${tempDocxPath}" -f docx -t html --standalone -o "${tempHtmlPath}"`;

    await execAsync(pandocHtmlCommand);
    const htmlContent = await readFile(tempHtmlPath, 'utf-8');

    // Clean up temp files
    try {
      await unlink(tempMarkdownPath);
      await unlink(tempDocxPath);
      await unlink(tempHtmlPath);
    } catch (cleanupError) {
      console.warn('Failed to clean up temp files:', cleanupError);
    }

    console.log('Successfully converted Gemini content to HTML preview using pandoc');
    return htmlContent;

  } catch (error) {
    console.error('Markdown to DOCX conversion error:', error);
    throw new Error('Failed to convert Gemini-processed content to DOCX: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

export async function POST(request: NextRequest) {
  try {
    const { templateId, markdown } = await request.json();

    if (!templateId || !markdown) {
      return NextResponse.json({ error: 'Missing template ID or markdown' }, { status: 400 });
    }

    console.log('Generating preview for template ID:', templateId);
    console.log('Processed markdown length:', markdown.length);

    // Generate updated DOCX and convert to HTML preview
    const htmlPreview = await generateUpdatedDocxPreview(templateId, markdown);

    // Highlight placeholders in the HTML content
    const highlightedHtml = htmlPreview.replace(
      /\{([A-Z_]+)\}|\[([A-Z_]+)\]|\$\{([A-Z_]+)\}|\{\{([A-Z_]+)\}\}/g,
      '<span style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 2px 6px; border-radius: 3px; font-weight: bold; color: #856404;">$&</span>'
    );

    // Return HTML content with proper styling for preview
    const styledHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Updated Document Preview</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 100%;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            box-sizing: border-box;
          }
          h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
          }
          h1 { font-size: 2.2em; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
          h1.title { font-size: 2.5em; text-align: center; border-bottom: none; }
          h2 { font-size: 1.8em; color: #34495e; }
          h2.subtitle { font-size: 1.4em; text-align: center; color: #7f8c8d; }
          h3 { font-size: 1.4em; color: #7f8c8d; }
          p { margin-bottom: 1em; }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          ul, ol {
            margin-bottom: 1em;
            padding-left: 2em;
          }
          li {
            margin-bottom: 0.5em;
          }
          /* Image handling */
          img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 1em auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          }
          /* Preserve Word formatting */
          .WordSection1 {
            margin: 0;
            padding: 0;
          }
          /* Better text alignment */
          .center, [style*="text-align: center"] {
            text-align: center;
          }
          .right, [style*="text-align: right"] {
            text-align: right;
          }
          /* Handle Word-style formatting */
          strong, b {
            font-weight: bold;
            color: #2c3e50;
          }
          em, i {
            font-style: italic;
          }
        </style>
      </head>
      <body>
        ${highlightedHtml}
      </body>
      </html>
    `;

    return new NextResponse(styledHtml, {
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('Generate preview error:', error);
    return NextResponse.json(
      { error: 'Failed to generate preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
