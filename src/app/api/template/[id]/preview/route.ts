import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

// GET - Serve the original DOCX file for preview
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    try {
      // Read the original DOCX file
      const docxBuffer = await readFile(docxPath);
      
      // Load template metadata for filename
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadataContent = await readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent);
        filename = metadata.name || filename;
      } catch (error) {
        console.warn('Could not load template metadata:', error);
      }

      // Return the DOCX file for preview/download
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (error) {
      return NextResponse.json(
        { error: 'Template file not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Template preview error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
