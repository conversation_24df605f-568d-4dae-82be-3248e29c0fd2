import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

const execAsync = promisify(exec);

// Convert DOCX to markdown using pandoc
async function convertDocxToMarkdown(docxPath: string, templateId?: string): Promise<{
  markdown: string;
  placeholders: string[];
}> {
  try {
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const tempMarkdownPath = join(uploadsDir, `addendum-temp-${Date.now()}.md`);

    // Use pandoc to convert DOCX to markdown with content control anchor preservation
    const pandocCommand = `pandoc "${docxPath}" -f docx -t markdown --wrap=none --preserve-tabs -o "${tempMarkdownPath}"`;

    console.log('Running pandoc command for addendum:', pandocCommand);
    await execAsync(pandocCommand);

    // Read the generated markdown
    const markdown = await readFile(tempMarkdownPath, 'utf-8');

    // Find placeholders in markdown using multiple patterns
    const placeholders = new Set<string>();

    // Common placeholder patterns - prioritize {FIELD} format for docx-templates
    const patterns = [
      /\{([A-Z_]+)\}/g,           // {CLIENT_NAME}, {PROJECT_NAME} - preferred format
      /\[([A-Z_]+)\]/g,           // [CLIENT_NAME], [PROJECT_NAME]
      /\$\{([A-Z_]+)\}/g,         // ${CLIENT_NAME}, ${PROJECT_NAME}
      /\{\{([A-Z_]+)\}\}/g,       // {{CLIENT_NAME}}, {{PROJECT_NAME}}
      /\{([a-z_]+)\}/g,           // {client_name}, {project_name}
      /\[([a-z_]+)\]/g,           // [client_name], [project_name]
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(markdown)) !== null) {
        placeholders.add(match[1]);
      }
    });

    console.log('Addendum placeholders found:', Array.from(placeholders));

    // Clean up temp file
    try {
      await execAsync(`rm "${tempMarkdownPath}"`);
    } catch (error) {
      console.warn('Failed to clean up temp markdown file:', error);
    }

    return {
      markdown,
      placeholders: Array.from(placeholders)
    };

  } catch (error) {
    console.error('DOCX to markdown conversion failed:', error);
    throw new Error(`Failed to convert DOCX to markdown: ${error}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const sowFile = formData.get('sowFile') as File;
    const addendumFile = formData.get('addendumFile') as File;
    const sowTemplateString = formData.get('sowTemplate') as string;
    const saveAsTemplate = formData.get('saveAsTemplate') === 'true';

    console.log('🔄 ADDENDUM UPLOAD: Processing upload...');
    console.log('SOW file:', sowFile?.name);
    console.log('SOW template provided:', !!sowTemplateString);
    console.log('Addendum file:', addendumFile?.name);

    // Check if we have either SOW file or SOW template + addendum file
    if (!addendumFile) {
      return NextResponse.json({
        error: 'Addendum template file is required'
      }, { status: 400 });
    }

    if (!sowFile && !sowTemplateString) {
      return NextResponse.json({
        error: 'Either SOW file or SOW template must be provided'
      }, { status: 400 });
    }

    // Validate file types
    if (sowFile && !sowFile.name.toLowerCase().endsWith('.docx')) {
      return NextResponse.json({
        error: 'SOW file must be a DOCX file'
      }, { status: 400 });
    }

    if (!addendumFile.name.toLowerCase().endsWith('.docx')) {
      return NextResponse.json({
        error: 'Addendum template must be a DOCX file'
      }, { status: 400 });
    }

    // Validate file sizes (10MB limit each)
    if (sowFile && sowFile.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        error: 'SOW file too large (max 10MB)'
      }, { status: 400 });
    }

    if (addendumFile.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        error: 'Addendum template too large (max 10MB)'
      }, { status: 400 });
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    
    try {
      await execAsync(`mkdir -p "${uploadsDir}"`);
    } catch (error) {
      console.warn('Failed to create uploads directory:', error);
    }

    // Process SOW (either file or template)
    let sowTemplateId: string;
    let sowMarkdown: string;
    let sowPlaceholders: string[];
    let sowFilePath: string;
    let sowName: string;

    if (sowFile) {
      // Process SOW file
      sowTemplateId = uuidv4();
      const sowFileName = `sow-${sowTemplateId}.docx`;
      sowFilePath = join(uploadsDir, sowFileName);

      const sowBytes = await sowFile.arrayBuffer();
      const sowBuffer = Buffer.from(sowBytes);
      await writeFile(sowFilePath, sowBuffer);

      console.log('✅ SOW file saved:', sowFilePath);

      // Convert SOW DOCX to markdown for analysis
      const sowConversion = await convertDocxToMarkdown(sowFilePath, sowTemplateId);
      sowMarkdown = sowConversion.markdown;
      sowPlaceholders = sowConversion.placeholders;
      sowName = sowFile.name;

      // Save SOW markdown version for analysis
      const sowMarkdownPath = join(uploadsDir, `sow-${sowTemplateId}.md`);
      await writeFile(sowMarkdownPath, sowMarkdown);
    } else {
      // Use provided SOW template
      const sowTemplate = JSON.parse(sowTemplateString);
      sowTemplateId = sowTemplate.id;
      sowMarkdown = sowTemplate.markdown;
      sowPlaceholders = sowTemplate.placeholders || [];
      sowFilePath = sowTemplate.filePath || '';
      sowName = sowTemplate.name;

      console.log('✅ SOW template loaded:', sowTemplate.name);
    }

    // Process Addendum template file
    const addendumTemplateId = uuidv4();
    const addendumFileName = `addendum-${addendumTemplateId}.docx`;
    const addendumFilePath = join(uploadsDir, addendumFileName);

    const addendumBytes = await addendumFile.arrayBuffer();
    const addendumBuffer = Buffer.from(addendumBytes);
    await writeFile(addendumFilePath, addendumBuffer);

    console.log('✅ Addendum template saved:', addendumFilePath);

    // Convert Addendum DOCX to markdown for processing
    const { markdown: addendumMarkdown, placeholders: addendumPlaceholders } = await convertDocxToMarkdown(addendumFilePath, addendumTemplateId);

    // Save Addendum markdown version for processing
    const addendumMarkdownPath = join(uploadsDir, `addendum-${addendumTemplateId}.md`);
    await writeFile(addendumMarkdownPath, addendumMarkdown);

    console.log('✅ ADDENDUM UPLOAD: Dual file processing complete');
    console.log('SOW placeholders:', sowPlaceholders.length);
    console.log('Addendum placeholders:', addendumPlaceholders.length);

    // Perform Gemini analysis on both documents
    console.log('🔍 ADDENDUM UPLOAD: Starting Gemini analysis...');

    try {
      // Make internal API call to Gemini analysis
      const baseUrl = process.env.NODE_ENV === 'production'
        ? process.env.NEXT_PUBLIC_BASE_URL || 'https://quantumrhino.cloud'
        : 'http://localhost:3000';

      const analysisResponse = await fetch(`${baseUrl}/api/gemini/analyze-addendum`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sowTemplateId: sowTemplateId,
          addendumTemplateId: addendumTemplateId,
          sowMarkdown: sowMarkdown,
          addendumMarkdown: addendumMarkdown
        }),
      });

      if (!analysisResponse.ok) {
        console.warn('⚠️ ADDENDUM UPLOAD: Gemini analysis failed, proceeding with basic analysis');
        // Continue with basic analysis if Gemini fails
      } else {
        const analysisResult = await analysisResponse.json();
        console.log('✅ ADDENDUM UPLOAD: Gemini analysis complete');

        // Return enhanced results with Gemini analysis
        return NextResponse.json({
          success: true,
          sowTemplate: {
            id: sowTemplateId,
            name: sowName,
            markdown: sowMarkdown,
            placeholders: sowPlaceholders,
            filePath: sowFilePath
          },
          addendumTemplate: {
            id: addendumTemplateId,
            name: addendumFile.name,
            markdown: addendumMarkdown,
            placeholders: addendumPlaceholders,
            filePath: addendumFilePath
          },
          analysis: analysisResult.analysis,
          uploadedAt: new Date().toISOString()
        });
      }
    } catch (analysisError) {
      console.warn('⚠️ ADDENDUM UPLOAD: Gemini analysis error:', analysisError);
      // Continue with basic analysis if Gemini fails
    }

    // Return basic processed files (fallback)
    return NextResponse.json({
      success: true,
      sowTemplate: {
        id: sowTemplateId,
        name: sowName,
        markdown: sowMarkdown,
        placeholders: sowPlaceholders,
        filePath: sowFilePath
      },
      addendumTemplate: {
        id: addendumTemplateId,
        name: addendumFile.name,
        markdown: addendumMarkdown,
        placeholders: addendumPlaceholders,
        filePath: addendumFilePath
      },
      uploadedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ ADDENDUM UPLOAD: Dual file upload failed:', error);
    return NextResponse.json(
      { error: 'Dual file upload failed', details: String(error) },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Addendum Dual File Upload API',
    endpoints: {
      'POST /api/addendum/upload': {
        description: 'Upload both SOW and Addendum template files for processing',
        required_fields: ['sowFile', 'addendumFile'],
        optional_fields: ['saveAsTemplate'],
        file_requirements: {
          sowFile: 'Original completed SOW in DOCX format (max 10MB)',
          addendumFile: 'Addendum template in DOCX format (max 10MB)'
        },
        output: {
          sowTemplate: 'Processed SOW with extracted context',
          addendumTemplate: 'Processed addendum template with placeholders'
        },
        example: {
          formData: {
            sowFile: 'completed-sow.docx',
            addendumFile: 'addendum-template.docx',
            saveAsTemplate: 'true'
          }
        }
      }
    },
    features: [
      'Dual file validation and processing',
      'Automatic placeholder detection',
      'Markdown conversion for AI processing',
      'Template storage and management',
      'Cross-document analysis preparation'
    ]
  });
}
