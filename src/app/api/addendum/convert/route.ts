import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { addendumTemplateId, markdown, clientName = 'Client' } = await request.json();

    if (!addendumTemplateId || !markdown) {
      return NextResponse.json({ error: 'Missing addendum template ID or markdown' }, { status: 400 });
    }

    console.log('🎯 ADDENDUM CONVERSION - template ID:', addendumTemplateId);
    console.log('Processed addendum markdown length:', markdown.length);

    // Use simplified conversion method matching existing SOW conversion
    const docxBuffer = await simplifiedAddendumConversion(markdown, addendumTemplateId);

    // Validate the generated DOCX buffer
    if (!docxBuffer || docxBuffer.length === 0) {
      throw new Error('Generated addendum DOCX buffer is empty');
    }

    console.log('✅ ADDENDUM CONVERSION: DOCX generated successfully, size:', docxBuffer.length, 'bytes');

    // Return the DOCX file
    return new NextResponse(docxBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="addendum-${clientName.replace(/[^a-zA-Z0-9]/g, '_')}-${Date.now()}.docx"`,
        'Content-Length': docxBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Addendum conversion error:', error);
    return NextResponse.json(
      { error: 'Addendum conversion failed', details: String(error) },
      { status: 500 }
    );
  }
}

// Simplified pandoc conversion for addendums matching SOW conversion approach
async function simplifiedAddendumConversion(
  markdown: string,
  addendumTemplateId: string
): Promise<Buffer> {
  try {
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${addendumTemplateId}.docx`);

    // Create temporary markdown file
    const tempMarkdownPath = join(uploadsDir, `addendum-temp-${Date.now()}.md`);
    await writeFile(tempMarkdownPath, markdown, 'utf-8');

    // Create output DOCX path
    const outputDocxPath = join(uploadsDir, `addendum-converted-${Date.now()}.docx`);

    // Build pandoc command - simplified and reliable
    let pandocCommand;

    try {
      // Check if reference document exists
      await readFile(originalDocxPath);
      pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${outputDocxPath}"`;
      console.log('✅ ADDENDUM CONVERSION: Using reference document for format preservation');
    } catch {
      console.warn('⚠️ ADDENDUM CONVERSION: Reference document not found, using basic conversion');
      pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx -o "${outputDocxPath}"`;
    }

    console.log('📄 ADDENDUM CONVERSION: Running pandoc command:', pandocCommand);

    try {
      await execAsync(pandocCommand);
      console.log('✅ ADDENDUM CONVERSION: Pandoc conversion successful');
    } catch (pandocError) {
      console.error('❌ ADDENDUM CONVERSION: Pandoc conversion failed:', pandocError);
      throw new Error(`Pandoc conversion failed: ${pandocError}`);
    }

    // Read the generated DOCX file
    const docxBuffer = await readFile(outputDocxPath);
    console.log('📄 ADDENDUM CONVERSION: DOCX file read, size:', docxBuffer.length, 'bytes');

    // Clean up temporary files
    try {
      await unlink(tempMarkdownPath);
      await unlink(outputDocxPath);
      console.log('🧹 ADDENDUM CONVERSION: Temporary files cleaned up');
    } catch (cleanupError) {
      console.warn('⚠️ ADDENDUM CONVERSION: Failed to clean up temporary files:', cleanupError);
    }

    return docxBuffer;

  } catch (error) {
    console.error('❌ ADDENDUM CONVERSION: Simplified conversion failed:', error);
    throw error;
  }
}

// Alternative conversion method using Docker (if needed for Railway deployment)
async function dockerAddendumConversion(
  markdown: string,
  addendumTemplateId: string
): Promise<Buffer> {
  try {
    console.log('🐳 ADDENDUM CONVERSION: Using Docker pandoc conversion...');
    
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${addendumTemplateId}.docx`);
    
    // Create temporary files
    const tempMarkdownPath = join(uploadsDir, `addendum-docker-temp-${Date.now()}.md`);
    const outputDocxPath = join(uploadsDir, `addendum-docker-converted-${Date.now()}.docx`);
    
    await writeFile(tempMarkdownPath, markdown, 'utf-8');

    // Docker pandoc command with volume mounting
    let dockerCommand;
    
    try {
      await readFile(originalDocxPath);
      dockerCommand = `docker run --rm -v "${uploadsDir}:/data" pandoc/core:latest -f markdown -t docx --reference-doc="/data/${addendumTemplateId}.docx" --wrap=preserve --preserve-tabs "/data/${tempMarkdownPath.split('/').pop()}" -o "/data/${outputDocxPath.split('/').pop()}"`;
      console.log('✅ ADDENDUM DOCKER: Using reference document for format preservation');
    } catch {
      dockerCommand = `docker run --rm -v "${uploadsDir}:/data" pandoc/core:latest -f markdown -t docx "/data/${tempMarkdownPath.split('/').pop()}" -o "/data/${outputDocxPath.split('/').pop()}"`;
      console.warn('⚠️ ADDENDUM DOCKER: Reference document not found, using basic conversion');
    }

    console.log('🐳 ADDENDUM DOCKER: Running command:', dockerCommand);
    await execAsync(dockerCommand);

    // Read the generated DOCX file
    const docxBuffer = await readFile(outputDocxPath);
    console.log('✅ ADDENDUM DOCKER: Conversion successful, size:', docxBuffer.length, 'bytes');

    // Clean up temporary files
    try {
      await unlink(tempMarkdownPath);
      await unlink(outputDocxPath);
      console.log('🧹 ADDENDUM DOCKER: Temporary files cleaned up');
    } catch (cleanupError) {
      console.warn('⚠️ ADDENDUM DOCKER: Failed to clean up temporary files:', cleanupError);
    }

    return docxBuffer;

  } catch (error) {
    console.error('❌ ADDENDUM DOCKER: Docker conversion failed:', error);
    throw error;
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Addendum Converter API',
    endpoints: {
      'POST /api/addendum/convert': {
        description: 'Convert processed addendum markdown to DOCX format',
        required_fields: ['addendumTemplateId', 'markdown'],
        optional_fields: ['clientName'],
        output: 'DOCX file download',
        example: {
          addendumTemplateId: 'template-123',
          markdown: 'Processed addendum content...',
          clientName: 'Acme Corp'
        }
      }
    },
    features: [
      'Reference document format preservation',
      'Pandoc conversion with fallback',
      'Docker support for containerized environments',
      'Automatic file cleanup',
      'Professional filename generation'
    ]
  });
}
