import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 ADDENDUM PROCESS: Starting addendum generation...');

    const { 
      sowMarkdown, 
      addendumMarkdown, 
      sowTemplateId, 
      addendumTemplateId, 
      userPrompt, 
      sowContext,
      isReviewStep = false 
    } = await request.json();

    if (!sowMarkdown || !addendumMarkdown || !addendumTemplateId) {
      return NextResponse.json({ 
        error: 'Missing required fields: sowMarkdown, addendumMarkdown, addendumTemplateId' 
      }, { status: 400 });
    }

    console.log('SOW Template ID:', sowTemplateId);
    console.log('Addendum Template ID:', addendumTemplateId);
    console.log('User prompt length:', userPrompt?.length || 0);
    console.log('SOW context fields:', sowContext?.length || 0);

    // Load company settings
    const companyInfo = await loadCompanySettings();
    console.log('Loaded company info:', companyInfo);

    // Check if we're in development mode with mock key
    console.log('Gemini API Key present:', !!process.env.GEMINI_API_KEY);

    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'YOUR_ACTUAL_GEMINI_API_KEY_HERE') {
      // Mock response for development
      console.log('Using mock Gemini API response for development');

      return NextResponse.json({
        updatedMarkdown: `${addendumMarkdown}\n\n<!-- Mock Mode: Addendum processed with SOW context -->`,
        message: 'Addendum processed successfully with SOW context (Mock Mode)'
      });
    }

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Create addendum-specific processing prompt
    const geminiPrompt = `
You are a PRECISION ADDENDUM DOCUMENT PROCESSOR. Your job is to create a professional contract addendum that properly references and extends the original Statement of Work.

**CRITICAL INSTRUCTIONS:**
- Maintain EXACT document structure from the addendum template
- Use information from the original SOW to ensure consistency
- Replace placeholder content with relevant, professional information
- Create proper cross-references to the original SOW
- Ensure legal consistency between the original SOW and addendum
- Generate content that feels natural and professionally written

**ORIGINAL SOW CONTEXT:**
\`\`\`
${sowMarkdown}
\`\`\`

**ADDENDUM TEMPLATE TO PROCESS:**
\`\`\`
${addendumMarkdown}
\`\`\`

**USER PROVIDED INFORMATION:**
${userPrompt || 'No additional user information provided'}

**SOW CONTEXT DATA:**
${sowContext ? JSON.stringify(sowContext, null, 2) : 'No SOW context provided'}

**COMPANY INFORMATION:**
${JSON.stringify(companyInfo, null, 2)}

**PROCESSING REQUIREMENTS:**

1. **STRUCTURE PRESERVATION:**
   - Keep ALL headers, sections, and document structure EXACTLY as in the template
   - Preserve all formatting, spacing, and layout
   - Maintain table structures, bullet points, and numbering
   - Do not add or remove sections unless explicitly needed

2. **CONTENT REPLACEMENT STRATEGY:**
   - Replace ALL placeholder content with relevant information
   - Use SOW context to ensure consistency (client names, project details, etc.)
   - Generate professional, contextually appropriate content
   - Ensure all dates, numbers, and references are realistic and consistent

3. **SOW CROSS-REFERENCING:**
   - Reference the original SOW appropriately (e.g., "Original SOW dated [DATE]")
   - Maintain consistency in client information, project names, and key details
   - Ensure addendum changes are clearly defined relative to the original SOW
   - Use proper legal language for document amendments

4. **INTELLIGENT CONTENT GENERATION:**
   - Generate realistic project details that align with the SOW context
   - Create appropriate timelines, budgets, and scope changes
   - Ensure all generated content is professional and legally sound
   - Replace template examples with contextually relevant information

5. **QUALITY ASSURANCE:**
   - Ensure no placeholder text remains (no [BRACKETS], {BRACES}, or XXXX)
   - Verify all cross-references are accurate and complete
   - Check that addendum changes are clearly articulated
   - Maintain professional tone throughout

**OUTPUT REQUIREMENTS:**
- Return ONLY the processed addendum markdown
- Ensure complete document with no placeholders
- Maintain exact structure while replacing all content
- Create professional, legally consistent addendum

Process the addendum template now, using the SOW context and user information to create a complete, professional contract addendum.`;

    try {
      // Step 1: Generate initial addendum content
      console.log('🤖 GEMINI: Calling API for addendum processing...');
      const result = await model.generateContent(geminiPrompt);
      console.log('✅ GEMINI: API call successful');

      const response = await result.response;
      let processedContent = response.text().trim();

      console.log('📊 GEMINI: Addendum content generated, length:', processedContent.length);

      // Step 2: Self-validation for addendum quality
      console.log('🔍 GEMINI: Starting addendum self-validation...');

      const validationPrompt = `
You are a SILENT ADDENDUM VALIDATOR. Review the processed addendum for quality and consistency.

**ORIGINAL SOW:**
\`\`\`
${sowMarkdown}
\`\`\`

**PROCESSED ADDENDUM:**
\`\`\`
${processedContent}
\`\`\`

**VALIDATION CHECKLIST:**
1. All placeholders replaced with appropriate content
2. Proper cross-references to original SOW
3. Consistent client/project information
4. Professional legal language
5. Clear definition of addendum changes
6. Proper document structure maintained

If any issues found, provide the corrected addendum. If no issues, return the addendum as-is.

Return ONLY the final addendum markdown - no explanations or comments.`;

      const validationResult = await model.generateContent(validationPrompt);
      const validationResponse = await validationResult.response;
      const finalContent = validationResponse.text().trim();

      console.log('✅ ADDENDUM PROCESS: Self-validation complete');

      return NextResponse.json({
        updatedMarkdown: finalContent,
        message: 'Addendum processed with SOW context and self-validated',
        validation: {
          selfValidated: true,
          sowContextUsed: !!sowContext,
          userPromptIncluded: !!userPrompt
        }
      });

    } catch (geminiError) {
      console.error('Gemini API specific error:', geminiError);

      // Fallback to basic processing if Gemini fails
      console.log('Falling back to basic processing due to Gemini error');
      return NextResponse.json({
        updatedMarkdown: `${addendumMarkdown}\n\n<!-- Fallback Mode: Gemini API failed, basic processing applied -->`,
        message: 'Addendum processed with fallback processing (Gemini API unavailable)'
      });
    }

  } catch (error) {
    console.error('Addendum processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: String(error) },
      { status: 500 }
    );
  }
}

// Load company settings with fallback
async function loadCompanySettings() {
  try {
    const settingsPath = join(process.cwd(), 'data', 'company-settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    return JSON.parse(settingsData);
  } catch (error) {
    console.warn('Company settings not found, using defaults');
    return {
      companyInfo: {
        companyName: 'QuantumRhino',
        contactName: 'Chase Vazquez',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Innovation Drive, Tech City, TC 12345',
        website: 'https://quantumrhino.com'
      },
      projectDefaults: {
        defaultProjectType: 'Web Application Development',
        defaultHourlyRate: '150',
        defaultTimeline: '8-12 weeks',
        defaultPaymentTerms: '50% upfront, 50% on completion'
      }
    };
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Addendum Processor API',
    endpoints: {
      'POST /api/addendum/process': {
        description: 'Process an addendum using SOW context and user input',
        required_fields: ['sowMarkdown', 'addendumMarkdown', 'addendumTemplateId'],
        optional_fields: ['sowTemplateId', 'userPrompt', 'sowContext', 'isReviewStep'],
        example: {
          sowMarkdown: 'Original SOW content...',
          addendumMarkdown: 'Addendum template content...',
          addendumTemplateId: 'template-123',
          userPrompt: 'Add scope for mobile app development',
          sowContext: [{ field: 'clientName', value: 'Acme Corp' }]
        }
      }
    }
  });
}
