import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile } from 'fs/promises';
import { join } from 'path';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 GEMINI ADDENDUM ANALYSIS: Starting dual-document analysis...');

    const requestData = await request.json();
    const { sowTemplateId, addendumTemplateId, sowMarkdown, addendumMarkdown } = requestData;

    if (!sowTemplateId || !addendumTemplateId || !sowMarkdown || !addendumMarkdown) {
      return NextResponse.json({ 
        error: 'Missing required fields: sowTemplateId, addendumTemplateId, sowMarkdown, addendumMarkdown' 
      }, { status: 400 });
    }

    console.log('SOW Template ID:', sowTemplateId);
    console.log('Addendum Template ID:', addendumTemplateId);
    console.log('SOW Markdown length:', sowMarkdown.length);
    console.log('Addendum Markdown length:', addendumMarkdown.length);

    // Load company settings for context
    const settings = await loadCompanySettings();

    // Analyze both documents with Gemini AI for addendum generation
    const analysis = await analyzeAddendumWithGemini(sowMarkdown, addendumMarkdown, settings);

    console.log('✅ GEMINI ADDENDUM ANALYSIS: Dual-document analysis complete');
    console.log('SOW context fields extracted:', analysis.sowContext?.length || 0);
    console.log('Addendum required fields detected:', analysis.addendumRequiredFields?.length || 0);
    console.log('Pre-fillable fields identified:', analysis.preFillableFields?.length || 0);

    return NextResponse.json({
      sowTemplateId,
      addendumTemplateId,
      analysis: {
        sowContext: analysis.sowContext,
        addendumRequiredFields: analysis.addendumRequiredFields,
        addendumOptionalFields: analysis.addendumOptionalFields,
        preFillableFields: analysis.preFillableFields,
        crossReferences: analysis.crossReferences,
        addendumType: analysis.addendumType,
        complexity: analysis.complexity,
        recommendations: analysis.recommendations,
        missingInformation: analysis.missingInformation
      },
      success: true
    });

  } catch (error) {
    console.error('❌ GEMINI ADDENDUM ANALYSIS: Dual-document analysis failed:', error);
    return NextResponse.json(
      { 
        error: 'Addendum analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
        fallbackAnalysis: createFallbackAddendumAnalysis(sowMarkdown, addendumMarkdown)
      },
      { status: 500 }
    );
  }
}

// Load company settings with fallback
async function loadCompanySettings() {
  try {
    const settingsPath = join(process.cwd(), 'data', 'company-settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    return JSON.parse(settingsData);
  } catch (error) {
    console.warn('Company settings not found, using defaults');
    return getDefaultSettings();
  }
}

function getDefaultSettings() {
  return {
    companyInfo: {
      companyName: 'QuantumRhino',
      contactName: 'Chase Vazquez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    },
    projectDefaults: {
      defaultProjectType: 'Web Application Development',
      defaultHourlyRate: '150',
      defaultTimeline: '8-12 weeks',
      defaultPaymentTerms: '50% upfront, 50% on completion'
    }
  };
}

// Analyze both SOW and Addendum documents with Gemini AI
async function analyzeAddendumWithGemini(sowMarkdown: string, addendumMarkdown: string, settings: any) {
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  const analysisPrompt = `
🔍 **INTELLIGENT ADDENDUM DUAL-DOCUMENT ANALYSIS**

You are an expert contract addendum analyst. Your task is to analyze BOTH a completed Statement of Work (SOW) and an addendum template to create intelligent addendum generation.

**CRITICAL INSTRUCTIONS:**
- Extract key information from the COMPLETED SOW to pre-fill addendum fields
- Identify what information from the SOW should carry over to the addendum
- Analyze the addendum template to determine what new information is needed
- Create cross-references between the original SOW and the addendum
- Focus on maintaining consistency between documents
- Identify fields that can be auto-filled vs. fields requiring new input

**ORIGINAL SOW DOCUMENT:**
\`\`\`
${sowMarkdown}
\`\`\`

**ADDENDUM TEMPLATE:**
\`\`\`
${addendumMarkdown}
\`\`\`

**COMPANY CONTEXT:**
${JSON.stringify(settings, null, 2)}

**ANALYSIS REQUIREMENTS:**

1. **SOW CONTEXT EXTRACTION:**
   - Extract client information (name, company, contact details)
   - Extract project details (name, type, scope, timeline)
   - Extract financial information (budget, rates, payment terms)
   - Extract deliverables and milestones
   - Extract any specific terms or conditions
   - Identify the original SOW reference number/identifier

2. **ADDENDUM REQUIREMENTS ANALYSIS:**
   - Identify all placeholder fields in the addendum template
   - Categorize fields as: auto-fillable from SOW, requires new input, or optional
   - Determine the type of addendum (scope change, timeline extension, budget modification, etc.)
   - Identify what new information is specifically needed for this addendum

3. **CROSS-REFERENCE MAPPING:**
   - Map SOW fields to corresponding addendum fields
   - Identify fields that should reference the original SOW
   - Create proper cross-referencing for legal consistency

4. **INTELLIGENT PRE-FILLING:**
   - Determine which SOW information can automatically populate addendum fields
   - Identify calculated fields (e.g., new totals, adjusted timelines)
   - Flag fields that need user input despite SOW context

**OUTPUT FORMAT (JSON):**
{
  "sowContext": [
    {
      "field": "clientName",
      "value": "extracted_value",
      "category": "client_info",
      "confidence": 0.95
    }
  ],
  "addendumRequiredFields": [
    {
      "field": "ADDENDUM_NUMBER",
      "label": "Addendum Number",
      "type": "text",
      "category": "document_info",
      "required": true,
      "canPreFill": false,
      "description": "Sequential addendum number for this SOW"
    }
  ],
  "addendumOptionalFields": [
    {
      "field": "ADDITIONAL_NOTES",
      "label": "Additional Notes",
      "type": "textarea",
      "category": "other",
      "required": false,
      "canPreFill": false
    }
  ],
  "preFillableFields": [
    {
      "addendumField": "ORIGINAL_SOW_CLIENT",
      "sowField": "clientName",
      "value": "extracted_client_name",
      "confidence": 0.98
    }
  ],
  "crossReferences": [
    {
      "sowReference": "Original SOW dated [DATE]",
      "addendumField": "SOW_REFERENCE",
      "relationship": "parent_document"
    }
  ],
  "addendumType": "scope_change|timeline_extension|budget_modification|terms_amendment|other",
  "complexity": "low|medium|high",
  "recommendations": [
    "Ensure new scope items are clearly defined",
    "Update project timeline to reflect changes"
  ],
  "missingInformation": [
    {
      "field": "NEW_DELIVERABLES",
      "reason": "Addendum appears to add scope but specific deliverables not defined",
      "priority": "high"
    }
  ]
}

Analyze both documents thoroughly and provide comprehensive JSON output for intelligent addendum generation.`;

  try {
    const result = await model.generateContent(analysisPrompt);
    const response = await result.response;
    const text = response.text();

    console.log('🤖 GEMINI ADDENDUM ANALYSIS: Raw response length:', text.length);

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in Gemini response');
    }

    const analysisData = JSON.parse(jsonMatch[0]);
    
    // Validate and enhance the analysis
    return enhanceAddendumAnalysis(analysisData, sowMarkdown, addendumMarkdown);

  } catch (error) {
    console.error('❌ GEMINI ADDENDUM ANALYSIS: Failed to analyze documents:', error);
    throw error;
  }
}

// Enhance and validate the analysis data
function enhanceAddendumAnalysis(analysisData: any, sowMarkdown: string, addendumMarkdown: string) {
  // Ensure all required fields exist
  const enhanced = {
    sowContext: analysisData.sowContext || [],
    addendumRequiredFields: analysisData.addendumRequiredFields || [],
    addendumOptionalFields: analysisData.addendumOptionalFields || [],
    preFillableFields: analysisData.preFillableFields || [],
    crossReferences: analysisData.crossReferences || [],
    addendumType: analysisData.addendumType || 'other',
    complexity: analysisData.complexity || 'medium',
    recommendations: analysisData.recommendations || [],
    missingInformation: analysisData.missingInformation || []
  };

  // Add fallback field detection if Gemini missed any
  const fallbackFields = detectFallbackAddendumFields(addendumMarkdown);

  // Merge with existing fields, avoiding duplicates
  const existingFieldNames = new Set(enhanced.addendumRequiredFields.map((f: any) => f.field));
  fallbackFields.forEach(field => {
    if (!existingFieldNames.has(field.field)) {
      enhanced.addendumRequiredFields.push(field);
    }
  });

  // Add basic SOW context if none detected
  if (enhanced.sowContext.length === 0) {
    enhanced.sowContext = extractBasicSOWContext(sowMarkdown);
  }

  return enhanced;
}

// Detect basic addendum fields as fallback
function detectFallbackAddendumFields(addendumMarkdown: string) {
  const fields = [];
  const patterns = [
    /\{([A-Z_]+)\}/g,
    /\[([A-Z_]+)\]/g,
    /\$\{([A-Z_]+)\}/g,
    /\{\{([A-Z_]+)\}\}/g
  ];

  const foundFields = new Set<string>();

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(addendumMarkdown)) !== null) {
      foundFields.add(match[1]);
    }
  });

  foundFields.forEach(fieldName => {
    fields.push({
      field: fieldName,
      label: fieldName.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
      type: 'text',
      category: 'other',
      required: true,
      canPreFill: false,
      description: `Field detected in addendum template: ${fieldName}`
    });
  });

  return fields;
}

// Extract basic SOW context as fallback
function extractBasicSOWContext(sowMarkdown: string) {
  const context = [];

  // Simple extraction patterns
  const clientMatch = sowMarkdown.match(/client[:\s]+([^\n\r]+)/i);
  if (clientMatch) {
    context.push({
      field: 'clientName',
      value: clientMatch[1].trim(),
      category: 'client_info',
      confidence: 0.7
    });
  }

  const projectMatch = sowMarkdown.match(/project[:\s]+([^\n\r]+)/i);
  if (projectMatch) {
    context.push({
      field: 'projectName',
      value: projectMatch[1].trim(),
      category: 'project_info',
      confidence: 0.7
    });
  }

  return context;
}

// Create fallback analysis when Gemini fails
function createFallbackAddendumAnalysis(sowMarkdown: string, addendumMarkdown: string) {
  return {
    sowContext: extractBasicSOWContext(sowMarkdown),
    addendumRequiredFields: detectFallbackAddendumFields(addendumMarkdown),
    addendumOptionalFields: [],
    preFillableFields: [],
    crossReferences: [],
    addendumType: 'other',
    complexity: 'medium',
    recommendations: [
      'Manual review recommended - automatic analysis failed',
      'Verify all field mappings between SOW and addendum'
    ],
    missingInformation: [
      {
        field: 'ANALYSIS_STATUS',
        reason: 'Automatic analysis failed, manual review required',
        priority: 'high'
      }
    ]
  };
}
