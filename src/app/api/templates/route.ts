import { NextRequest, NextResponse } from 'next/server';
import { withOrgContext } from '@/lib/auth/portal-auth';

// GET - Load templates list
export const GET = withOrgContext(async (request, context) => {
  try {
    const { db, user } = context;

    // Get templates from organization database
    const templates = await db.$queryRaw<any[]>`
      SELECT 
        id,
        name,
        description,
        template_data as "templateData",
        category,
        tags,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM sow_templates
      WHERE (owner_id = ${user.id}::uuid OR is_shared = true)
      ORDER BY created_at DESC
    `;

    return NextResponse.json(templates);
  } catch (error) {
    console.error('Templates load error:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
});

// POST - Create new template
export const POST = withOrgContext(async (request, context) => {
  try {
    const { db, user } = context;
    const body = await request.json();
    const { name, description, templateData, category, tags } = body;

    if (!name || !templateData) {
      return NextResponse.json(
        { error: 'Name and template data are required' },
        { status: 400 }
      );
    }

    // Create template in organization database
    const result = await db.$queryRaw<any[]>`
      INSERT INTO sow_templates (
        name,
        description,
        template_data,
        category,
        tags,
        owner_id,
        is_shared
      ) VALUES (
        ${name},
        ${description || null},
        ${JSON.stringify(templateData)}::jsonb,
        ${category || null},
        ${tags || []}::text[],
        ${user.id}::uuid,
        false
      )
      RETURNING 
        id,
        name,
        description,
        template_data as "templateData",
        category,
        tags,
        created_at as "createdAt"
    `;

    return NextResponse.json(result[0], { status: 201 });
  } catch (error) {
    console.error('Template creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
});

// DELETE - Remove a template
export const DELETE = withOrgContext(async (request, context) => {
  try {
    const { db, user } = context;
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Delete template (only if user owns it)
    const result = await db.$queryRaw<any[]>`
      DELETE FROM sow_templates
      WHERE id = ${templateId}::uuid
        AND owner_id = ${user.id}::uuid
      RETURNING id
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { error: 'Template not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Template removed successfully'
    });

  } catch (error) {
    console.error('Template deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete template' },
      { status: 500 }
    );
  }
});