import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing template save functionality...');

    // Test data for template saving
    const testTemplateData = {
      name: `Test Template ${Date.now()}`,
      markdown: `# Test SOW Template

## Project Overview
This is a test template created to verify the template saving functionality.

## Scope of Work
- Test item 1
- Test item 2
- Test item 3

## Timeline
The project will be completed within the agreed timeframe.

## Budget
Budget details will be provided based on requirements.

---
*Generated for testing purposes*`,
      sections: ['overview', 'scope', 'timeline', 'budget'],
      formData: {
        clientName: 'Test Client',
        projectName: 'Test Project',
        companyName: 'Test Company'
      }
    };

    console.log('📤 Sending test template data to save endpoint...');

    // Call the save template API
    const saveResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/templates/save`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testTemplateData),
    });

    if (!saveResponse.ok) {
      const errorData = await saveResponse.json();
      throw new Error(`Save failed: ${errorData.error}`);
    }

    const saveResult = await saveResponse.json();
    console.log('✅ Template saved successfully:', saveResult);

    // Test loading the saved template
    console.log('📥 Testing template loading...');
    const loadResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/templates/${saveResult.id}`);

    if (!loadResponse.ok) {
      const errorData = await loadResponse.json();
      throw new Error(`Load failed: ${errorData.error}`);
    }

    const loadResult = await loadResponse.json();
    console.log('✅ Template loaded successfully:', loadResult.name);

    // Test loading all templates
    console.log('📋 Testing templates list...');
    const listResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/templates/save`);

    if (!listResponse.ok) {
      const errorData = await listResponse.json();
      throw new Error(`List failed: ${errorData.error}`);
    }

    const listResult = await listResponse.json();
    console.log('✅ Templates list loaded:', listResult.length, 'templates');

    return NextResponse.json({
      success: true,
      message: 'Template save/load functionality test completed successfully',
      results: {
        saved: saveResult,
        loaded: loadResult,
        totalTemplates: listResult.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Template test failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Template test failed', 
        details: String(error) 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Template Save/Load Test API',
    status: 'ready',
    description: 'POST to this endpoint to test template saving and loading functionality',
    endpoints: {
      'POST /api/templates/test': 'Run template save/load test'
    }
  });
}
