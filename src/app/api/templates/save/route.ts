import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

interface SavedTemplate {
  id: string;
  name: string;
  markdown: string;
  sections: any[];
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
  savedFormData?: any; // Store form data from SOW generator
}

// Get templates file path - use Railway storage path for consistency
function getTemplatesFilePath(): string {
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
  return join(storageRoot, 'saved-templates.json');
}

// Load existing templates
async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const templatesPath = getTemplatesFilePath();
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is empty, return empty array
    return [];
  }
}

// Save templates to file
async function saveTemplates(templates: SavedTemplate[]): Promise<void> {
  const templatesPath = getTemplatesFilePath();
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');

  // Ensure storage directory exists
  try {
    await mkdir(storageRoot, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  await writeFile(templatesPath, JSON.stringify(templates, null, 2));
  console.log('✅ Template saved to:', templatesPath);
}

export async function POST(request: NextRequest) {
  try {
    const { name, markdown, sections, formData } = await request.json();

    if (!name || !markdown) {
      return NextResponse.json({ error: 'Name and markdown are required' }, { status: 400 });
    }

    console.log('💾 Saving template:', { name, hasMarkdown: !!markdown, hasFormData: !!formData });

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 Loaded existing templates:', templates.length);

    // Create new template
    const newTemplate: SavedTemplate = {
      id: uuidv4(),
      name: name,
      markdown: markdown,
      sections: sections || [],
      createdAt: new Date().toISOString(),
      usageCount: 0,
      savedFormData: formData || null // Store the form data from SOW generator
    };

    // Add to templates array
    templates.push(newTemplate);

    // Save back to file
    await saveTemplates(templates);
    console.log('✅ Template saved successfully:', newTemplate.id);

    return NextResponse.json({
      id: newTemplate.id,
      message: 'Template saved successfully'
    });

  } catch (error) {
    console.error('❌ Template save error:', error);
    return NextResponse.json(
      { error: 'Failed to save template' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const templates = await loadTemplates();
    
    // Sort by most recently used, then by creation date
    templates.sort((a, b) => {
      if (a.lastUsed && b.lastUsed) {
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      }
      if (a.lastUsed && !b.lastUsed) return -1;
      if (!a.lastUsed && b.lastUsed) return 1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json(templates);

  } catch (error) {
    console.error('Template load error:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a saved template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ Deleting saved template:', templateId);

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 Found templates before deletion:', templates.length);

    // Filter out the template to delete
    const updatedTemplates = templates.filter(t => t.id !== templateId);

    if (updatedTemplates.length === templates.length) {
      console.log('❌ Template not found:', templateId);
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Save updated templates list
    await saveTemplates(updatedTemplates);
    console.log('✅ Template deleted successfully. Remaining templates:', updatedTemplates.length);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
      remainingCount: updatedTemplates.length
    });

  } catch (error) {
    console.error('❌ Failed to delete template:', error);
    return NextResponse.json(
      { error: 'Failed to delete template', details: String(error) },
      { status: 500 }
    );
  }
}
