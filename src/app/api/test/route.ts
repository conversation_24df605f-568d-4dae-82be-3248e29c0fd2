import { NextResponse } from 'next/server';

export async function GET() {
  console.log('🧪 Test endpoint called successfully');
  
  return NextResponse.json({
    status: 'success',
    message: 'SOW Generator API is working',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    railwayEnvironment: process.env.RAILWAY_ENVIRONMENT_NAME,
    databaseConfigured: !!process.env.DATABASE_URL,
    portalIntegrationReady: process.env.PORTAL_INTEGRATION_READY
  });
}

export async function POST() {
  console.log('🧪 Test POST endpoint called');
  
  return NextResponse.json({
    status: 'success',
    message: 'POST request successful',
    timestamp: new Date().toISOString()
  });
}
