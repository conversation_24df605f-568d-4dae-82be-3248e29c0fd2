import { NextRequest, NextResponse } from 'next/server';
import { readdir, readFile, stat } from 'fs/promises';
import { join } from 'path';

// GET - Debug storage paths and file structure for Railway deployment
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 STORAGE DEBUG: Starting storage diagnostics...');
    
    const diagnostics: any = {
      environment: {
        STORAGE_PATH: process.env.STORAGE_PATH || 'not set',
        NODE_ENV: process.env.NODE_ENV || 'not set',
        cwd: process.cwd()
      },
      paths: {},
      files: {},
      errors: []
    };

    // Check different storage paths
    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    
    diagnostics.paths = {
      storageRoot,
      uploadsDir,
      savedTemplatesPath: join(storageRoot, 'saved-templates.json'),
      uploadedTemplatesPath: join(uploadsDir, 'templates.json')
    };

    // Check if directories exist and list contents
    for (const [pathName, pathValue] of Object.entries(diagnostics.paths)) {
      try {
        const stats = await stat(pathValue as string);
        diagnostics.files[pathName] = {
          exists: true,
          isDirectory: stats.isDirectory(),
          isFile: stats.isFile(),
          size: stats.size,
          modified: stats.mtime
        };

        // If it's a directory, list contents
        if (stats.isDirectory()) {
          try {
            const contents = await readdir(pathValue as string);
            diagnostics.files[pathName].contents = contents;
          } catch (error) {
            diagnostics.files[pathName].contentsError = String(error);
          }
        }

        // If it's a JSON file, try to read and parse it
        if (stats.isFile() && (pathValue as string).endsWith('.json')) {
          try {
            const content = await readFile(pathValue as string, 'utf-8');
            const parsed = JSON.parse(content);
            diagnostics.files[pathName].jsonContent = Array.isArray(parsed) ? 
              { isArray: true, length: parsed.length, items: parsed.slice(0, 3) } : 
              { isArray: false, keys: Object.keys(parsed) };
          } catch (error) {
            diagnostics.files[pathName].jsonError = String(error);
          }
        }

      } catch (error) {
        diagnostics.files[pathName] = {
          exists: false,
          error: String(error)
        };
        diagnostics.errors.push(`${pathName}: ${error}`);
      }
    }

    // Check for any template files in uploads directory
    try {
      const uploadsContents = await readdir(uploadsDir);
      const templateFiles = uploadsContents.filter(file => 
        file.endsWith('.docx') || file.endsWith('.json') || file.endsWith('.md')
      );
      
      diagnostics.templateFiles = {
        total: templateFiles.length,
        docx: templateFiles.filter(f => f.endsWith('.docx')).length,
        json: templateFiles.filter(f => f.endsWith('.json')).length,
        md: templateFiles.filter(f => f.endsWith('.md')).length,
        files: templateFiles.slice(0, 10) // Show first 10 files
      };
    } catch (error) {
      diagnostics.templateFiles = { error: String(error) };
    }

    console.log('✅ STORAGE DEBUG: Diagnostics complete');
    return NextResponse.json(diagnostics, { status: 200 });

  } catch (error) {
    console.error('❌ STORAGE DEBUG: Failed to run diagnostics:', error);
    return NextResponse.json(
      { 
        error: 'Failed to run storage diagnostics',
        details: String(error)
      },
      { status: 500 }
    );
  }
}
