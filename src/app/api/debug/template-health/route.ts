import { NextRequest, NextResponse } from 'next/server';

// GET - Health check for template loading system
export async function GET(request: NextRequest) {
  try {
    console.log('🏥 TEMPLATE HEALTH: Starting health check...');
    
    const healthCheck: any = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {},
      errors: []
    };

    // Test 1: Check saved templates API
    try {
      console.log('🔍 TEMPLATE HEALTH: Testing saved templates API...');
      const savedTemplatesResponse = await fetch(`${request.nextUrl.origin}/api/templates/save`);
      healthCheck.checks.savedTemplatesAPI = {
        status: savedTemplatesResponse.ok ? 'pass' : 'fail',
        statusCode: savedTemplatesResponse.status,
        responseTime: Date.now()
      };
      
      if (savedTemplatesResponse.ok) {
        const savedTemplates = await savedTemplatesResponse.json();
        healthCheck.checks.savedTemplatesAPI.templateCount = savedTemplates.length;
      }
    } catch (error) {
      healthCheck.checks.savedTemplatesAPI = {
        status: 'error',
        error: String(error)
      };
      healthCheck.errors.push(`Saved templates API: ${error}`);
    }

    // Test 2: Check uploaded templates API
    try {
      console.log('🔍 TEMPLATE HEALTH: Testing uploaded templates API...');
      const uploadedTemplatesResponse = await fetch(`${request.nextUrl.origin}/api/template/list`);
      healthCheck.checks.uploadedTemplatesAPI = {
        status: uploadedTemplatesResponse.ok ? 'pass' : 'fail',
        statusCode: uploadedTemplatesResponse.status,
        responseTime: Date.now()
      };
      
      if (uploadedTemplatesResponse.ok) {
        const uploadedTemplates = await uploadedTemplatesResponse.json();
        healthCheck.checks.uploadedTemplatesAPI.templateCount = uploadedTemplates.length;
      }
    } catch (error) {
      healthCheck.checks.uploadedTemplatesAPI = {
        status: 'error',
        error: String(error)
      };
      healthCheck.errors.push(`Uploaded templates API: ${error}`);
    }

    // Test 3: Check storage diagnostics
    try {
      console.log('🔍 TEMPLATE HEALTH: Testing storage diagnostics...');
      const storageResponse = await fetch(`${request.nextUrl.origin}/api/debug/storage`);
      healthCheck.checks.storageAPI = {
        status: storageResponse.ok ? 'pass' : 'fail',
        statusCode: storageResponse.status
      };
      
      if (storageResponse.ok) {
        const storageData = await storageResponse.json();
        healthCheck.checks.storageAPI.storageInfo = {
          hasStoragePath: !!storageData.environment.STORAGE_PATH,
          uploadsExists: storageData.files.uploadsDir?.exists || false,
          storageExists: storageData.files.storageRoot?.exists || false
        };
      }
    } catch (error) {
      healthCheck.checks.storageAPI = {
        status: 'error',
        error: String(error)
      };
      healthCheck.errors.push(`Storage API: ${error}`);
    }

    // Determine overall health status
    const failedChecks = Object.values(healthCheck.checks).filter((check: any) => 
      check.status === 'fail' || check.status === 'error'
    );
    
    if (failedChecks.length > 0) {
      healthCheck.status = 'unhealthy';
    } else if (healthCheck.errors.length > 0) {
      healthCheck.status = 'degraded';
    }

    console.log('✅ TEMPLATE HEALTH: Health check complete, status:', healthCheck.status);
    
    return NextResponse.json(healthCheck, { 
      status: healthCheck.status === 'healthy' ? 200 : 503 
    });

  } catch (error) {
    console.error('❌ TEMPLATE HEALTH: Health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Health check failed',
        details: String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
