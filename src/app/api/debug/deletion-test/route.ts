import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

// GET - Test deletion functionality health
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DELETION TEST: Starting deletion functionality health check');
    
    // Use Railway storage path if available
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    
    const results = {
      timestamp: new Date().toISOString(),
      storageInfo: {
        uploadsDir,
        isRailway: !!process.env.STORAGE_PATH
      },
      templates: {
        uploaded: { count: 0, available: false, error: null },
        saved: { count: 0, available: false, error: null }
      },
      sows: {
        count: 0,
        available: false,
        error: null
      },
      apiEndpoints: {
        uploadedTemplateDelete: '/api/template?id={id}',
        savedTemplateDelete: '/api/templates/save?id={id}',
        databaseTemplateDelete: '/api/templates?id={id}',
        sowDelete: '/api/sow/{id}'
      }
    };

    // Check uploaded templates
    try {
      const templatesListPath = join(uploadsDir, 'templates.json');
      const templatesData = await readFile(templatesListPath, 'utf-8');
      const templates = JSON.parse(templatesData);
      results.templates.uploaded.count = templates.length;
      results.templates.uploaded.available = true;
      console.log('✅ DELETION TEST: Found', templates.length, 'uploaded templates');
    } catch (error) {
      results.templates.uploaded.error = String(error);
      console.log('❌ DELETION TEST: No uploaded templates found:', error);
    }

    // Check saved templates
    try {
      const savedTemplatesPath = join(uploadsDir, 'saved-templates.json');
      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      results.templates.saved.count = savedTemplates.length;
      results.templates.saved.available = true;
      console.log('✅ DELETION TEST: Found', savedTemplates.length, 'saved templates');
    } catch (error) {
      results.templates.saved.error = String(error);
      console.log('❌ DELETION TEST: No saved templates found:', error);
    }

    // Check SOWs
    try {
      const sowsListPath = join(uploadsDir, 'sows.json');
      const sowsData = await readFile(sowsListPath, 'utf-8');
      const sows = JSON.parse(sowsData);
      results.sows.count = sows.length;
      results.sows.available = true;
      console.log('✅ DELETION TEST: Found', sows.length, 'SOWs');
    } catch (error) {
      results.sows.error = String(error);
      console.log('❌ DELETION TEST: No SOWs found:', error);
    }

    console.log('🔍 DELETION TEST: Health check completed');

    return NextResponse.json({
      status: 'success',
      message: 'Deletion functionality health check completed',
      results
    });

  } catch (error) {
    console.error('❌ DELETION TEST: Health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Deletion test failed', 
        details: String(error) 
      },
      { status: 500 }
    );
  }
}

// POST - Test actual deletion functionality (for testing purposes)
export async function POST(request: NextRequest) {
  try {
    const { type, id } = await request.json();
    
    if (!type || !id) {
      return NextResponse.json(
        { error: 'Type and ID are required for deletion test' },
        { status: 400 }
      );
    }

    console.log('🧪 DELETION TEST: Testing deletion of', type, 'with ID:', id);

    let endpoint = '';
    let response;

    switch (type) {
      case 'uploaded-template':
        endpoint = `/api/template?id=${id}`;
        break;
      case 'saved-template':
        endpoint = `/api/templates/save?id=${id}`;
        break;
      case 'database-template':
        endpoint = `/api/templates?id=${id}`;
        break;
      case 'sow':
        endpoint = `/api/sow/${id}`;
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid type. Use: uploaded-template, saved-template, database-template, or sow' },
          { status: 400 }
        );
    }

    console.log('🧪 DELETION TEST: Making DELETE request to:', endpoint);

    // Make the deletion request
    const baseUrl = request.headers.get('host');
    const protocol = request.headers.get('x-forwarded-proto') || 'http';
    const fullUrl = `${protocol}://${baseUrl}${endpoint}`;

    response = await fetch(fullUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const responseData = await response.json().catch(() => ({ message: 'No JSON response' }));

    console.log('🧪 DELETION TEST: Response status:', response.status, 'Data:', responseData);

    return NextResponse.json({
      status: response.ok ? 'success' : 'error',
      message: `Deletion test for ${type} completed`,
      testResults: {
        endpoint,
        httpStatus: response.status,
        success: response.ok,
        responseData
      }
    });

  } catch (error) {
    console.error('❌ DELETION TEST: Test failed:', error);
    return NextResponse.json(
      { 
        status: 'error',
        error: 'Deletion test failed', 
        details: String(error) 
      },
      { status: 500 }
    );
  }
}
