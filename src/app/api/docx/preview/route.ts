import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    const { markdown, templateId, clientName = 'Client' } = await request.json();

    if (!markdown) {
      return NextResponse.json({ error: 'Missing markdown content' }, { status: 400 });
    }

    console.log('📄 DOCX PREVIEW: Starting markdown to DOCX conversion for preview...');
    console.log('Template ID:', templateId);
    console.log('Markdown length:', markdown.length);

    // Create temporary files - FIXED: Use Railway storage path
    const timestamp = Date.now();
    const tempDir = process.env.STORAGE_PATH ? path.join(process.env.STORAGE_PATH, 'uploads') : path.join(process.cwd(), 'uploads');
    const markdownFile = path.join(tempDir, `preview-${timestamp}.md`);
    const docxFile = path.join(tempDir, `preview-${timestamp}.docx`);

    // Ensure uploads directory exists
    try {
      await fs.access(tempDir);
    } catch {
      await fs.mkdir(tempDir, { recursive: true });
    }

    // Write markdown to temporary file
    await fs.writeFile(markdownFile, markdown, 'utf8');

    // Convert markdown to DOCX using pandoc with reference document for format preservation
    let pandocCommand;
    
    if (templateId) {
      // Use original template as reference document to preserve formatting (SAME AS EXISTING SYSTEM)
      const originalDocxPath = path.join(tempDir, `${templateId}.docx`);
      
      try {
        await fs.access(originalDocxPath);
        pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx --reference-doc="${originalDocxPath}" --wrap=preserve --preserve-tabs -o "${docxFile}"`;
        console.log('📄 DOCX PREVIEW: Using reference document for format preservation');
      } catch {
        console.warn('📄 DOCX PREVIEW: Original template not found, using basic conversion');
        pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx -o "${docxFile}"`;
      }
    } else {
      pandocCommand = `pandoc "${markdownFile}" -f markdown -t docx -o "${docxFile}"`;
    }
    
    console.log('📄 DOCX PREVIEW: Running pandoc command:', pandocCommand);
    
    try {
      await execAsync(pandocCommand);
      console.log('📄 DOCX PREVIEW: Pandoc conversion successful');
    } catch (pandocError) {
      console.error('📄 DOCX PREVIEW: Pandoc conversion failed:', pandocError);
      throw new Error(`Pandoc conversion failed: ${pandocError}`);
    }

    // Read the generated DOCX file as buffer for preview
    const docxBuffer = await fs.readFile(docxFile);
    
    console.log('📄 DOCX PREVIEW: DOCX file generated for preview, size:', docxBuffer.length, 'bytes');

    // Clean up temporary files
    try {
      await fs.unlink(markdownFile);
      await fs.unlink(docxFile);
      console.log('📄 DOCX PREVIEW: Temporary files cleaned up');
    } catch (cleanupError) {
      console.warn('📄 DOCX PREVIEW: Failed to clean up temporary files:', cleanupError);
    }

    // Return the DOCX buffer as JSON for preview (not download)
    return NextResponse.json({
      success: true,
      docxBuffer: Array.from(docxBuffer), // Convert buffer to array for JSON transport
      size: docxBuffer.length,
      message: 'DOCX preview generated successfully'
    });

  } catch (error) {
    console.error('📄 DOCX PREVIEW: Conversion error:', error);
    
    return NextResponse.json({
      error: 'DOCX preview generation failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'DOCX Preview API',
    description: 'Converts markdown to DOCX format for preview using pandoc with reference document',
    endpoints: {
      'POST /api/docx/preview': {
        description: 'Convert markdown to DOCX buffer for preview',
        required_fields: ['markdown'],
        optional_fields: ['templateId', 'clientName'],
        response: 'DOCX buffer as JSON array'
      }
    },
    requirements: [
      'pandoc must be installed on the system',
      'uploads directory must be writable',
      'Original template DOCX file must exist for format preservation'
    ]
  });
}
