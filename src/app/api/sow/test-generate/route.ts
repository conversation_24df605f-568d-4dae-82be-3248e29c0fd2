import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Test SOW generation endpoint called');
    
    const body = await request.json();
    const { 
      companyName = 'Test Company', 
      projectType = 'web-application', 
      budget = '50000',
      timeline = '12 weeks',
      clientName = 'Test Client',
      clientEmail = '<EMAIL>',
      projectDescription = 'Test project description'
    } = body;

    console.log('📝 Generating test SOW with data:', { companyName, projectType, budget });

    // Generate a simple test SOW
    const testSOW = {
      id: `test-sow-${Date.now()}`,
      companyName,
      projectType,
      budget,
      timeline,
      clientName,
      clientEmail,
      projectDescription,
      generatedAt: new Date().toISOString(),
      content: `
# Statement of Work - ${companyName}

## Project Overview
**Client:** ${clientName} (${clientEmail})
**Company:** ${companyName}
**Project Type:** ${projectType}
**Budget:** $${budget}
**Timeline:** ${timeline}

## Project Description
${projectDescription}

## Deliverables
- Project planning and requirements gathering
- Design and development
- Testing and quality assurance
- Deployment and launch
- Documentation and training

## Timeline
The project will be completed within ${timeline} with regular milestone reviews.

## Investment
Total project investment: $${budget}

---
*Generated by QuantumRhino SOW Generator*
*Date: ${new Date().toLocaleDateString()}*
      `.trim()
    };

    console.log('✅ Test SOW generated successfully');

    return NextResponse.json({
      success: true,
      sow: testSOW,
      message: 'Test SOW generated successfully'
    });

  } catch (error) {
    console.error('❌ Test SOW generation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate test SOW', 
        details: String(error) 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Test SOW Generator API',
    status: 'ready',
    endpoints: {
      'POST /api/sow/test-generate': 'Generate a test SOW'
    },
    example: {
      companyName: 'Acme Corp',
      projectType: 'web-application',
      budget: '50000',
      timeline: '12 weeks',
      clientName: 'John Doe',
      clientEmail: '<EMAIL>',
      projectDescription: 'A modern web application'
    }
  });
}
