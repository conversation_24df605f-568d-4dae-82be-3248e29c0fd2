import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getDbRouter } from '../services/database-router';
import { ensureUserInOrgDatabase } from '../services/user-sync.service';

interface User {
  id: string;
  email: string;
  displayName: string;
  organizationId: string;
  role: string;
}

interface OrgContext {
  db: PrismaClient;
  user: User;
  organizationId: string;
  organizationSlug: string;
}

type OrgHandler = (request: NextRequest, context: OrgContext) => Promise<NextResponse>;

/**
 * Higher-order function that provides organization context to API routes
 */
export function withOrgContext(handler: OrgHandler) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // For development, use a mock user context
      // In production, this would extract from JWT token or session
      const mockUser: User = {
        id: 'dev-user-1',
        email: '<EMAIL>',
        displayName: 'Development User',
        organizationId: 'dev-org-1',
        role: 'admin'
      };

      const organizationId = mockUser.organizationId;
      
      // Get database router and connection
      const dbRouter = getDbRouter();
      
      // Get organization info
      const org = await dbRouter.getOrganization(organizationId);
      if (!org) {
        return NextResponse.json(
          { error: 'Organization not found' },
          { status: 404 }
        );
      }

      if (org.status !== 'active') {
        return NextResponse.json(
          { error: 'Organization inactive' },
          { status: 403 }
        );
      }

      // Get organization database connection
      const db = await dbRouter.getConnection(organizationId);
      
      // Ensure user exists in organization database
      await ensureUserInOrgDatabase(db, mockUser);

      // Create context
      const context: OrgContext = {
        db,
        user: mockUser,
        organizationId,
        organizationSlug: org.slug
      };

      // Call the handler with context
      return await handler(request, context);
      
    } catch (error) {
      console.error('Organization context error:', error);
      return NextResponse.json(
        { error: 'Failed to establish organization context' },
        { status: 500 }
      );
    }
  };
}
