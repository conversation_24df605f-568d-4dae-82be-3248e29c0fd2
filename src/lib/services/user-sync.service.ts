/**
 * User Synchronization Service
 * 
 * This service ensures that users from the Portal exist in organization databases
 * when they first access the organization.
 */

import { PrismaClient } from '@prisma/client';

interface PortalUser {
  id: string;
  email: string;
  displayName: string;
  role: string;
}

/**
 * Ensure user exists in organization database
 * Creates the user if they don't exist, updates if they do
 */
export async function ensureUserInOrgDatabase(
  db: PrismaClient,
  portalUser: PortalUser
): Promise<void> {
  try {
    // Check if user exists by their portal ID
    const existingUser = await db.user.findFirst({
      where: {
        OR: [
          { id: portalUser.id },
          { email: portalUser.email },
          { azureId: portalUser.id }
        ]
      }
    });

    if (!existingUser) {
      // Create new user in organization database
      await db.user.create({
        data: {
          id: portalUser.id, // Use the same ID from portal
          email: portalUser.email,
          displayName: portalUser.displayName,
          azureId: portalUser.id,
          role: mapPortalRoleToLocalRole(portalUser.role),
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      });
    } else if (existingUser.id !== portalUser.id) {
      // If user exists with different ID (e.g., by email), update to sync IDs
      await db.user.update({
        where: { id: existingUser.id },
        data: {
          azureId: portalUser.id,
          displayName: portalUser.displayName,
          updatedAt: new Date(),
        }
      });
    } else {
      // Update user info to stay in sync with portal
      await db.user.update({
        where: { id: existingUser.id },
        data: {
          displayName: portalUser.displayName,
          lastActiveAt: new Date(),
          updatedAt: new Date(),
        }
      });
    }
  } catch (error) {
    console.error('Failed to sync user to organization database:', error);
    // Don't throw - allow request to continue even if sync fails
  }
}

/**
 * Map portal roles to local document roles
 */
function mapPortalRoleToLocalRole(portalRole: string): string {
  // Map organization roles to document system roles
  switch (portalRole.toUpperCase()) {
    case 'OWNER':
    case 'ADMIN':
      return 'ADMIN';
    case 'MEMBER':
      return 'EDITOR';
    case 'GUEST':
    default:
      return 'VIEWER';
  }
}