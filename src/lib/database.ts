import { PrismaClient } from '@prisma/client';
import { getDbRouter } from './services/database-router';

export const getOrgDb = async (organizationId: string) => {
  const dbRouter = getDbRouter();
  return await dbRouter.getConnection(organizationId);
};

// Database service functions
export class DatabaseService {

  /**
   * Get or create user by email
   */
  static async getOrCreateUser(organizationId: string, email: string, name?: string, avatar?: string) {
    const db = await getOrgDb(organizationId);
    let user = await db.user.findUnique({
      where: { email },
      include: { organization: true }
    });

    if (!user) {
      user = await db.user.create({
        data: {
          email,
          name: name || email.split('@')[0],
          avatar
        },
        include: { organization: true }
      });
    }

    return user;
  }

  /**
   * Get user templates
   */
  static async getUserTemplates(organizationId: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Create template
   */
  static async createTemplate(organizationId: string, data: {
    name: string;
    description?: string;
    filePath: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    userId: string;
    isDefault?: boolean;
  }) {
    const db = await getOrgDb(organizationId);
    return await db.template.create({
      data
    });
  }

  /**
   * Get user companies
   */
  static async getUserCompanies(organizationId: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.company.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });
  }

  /**
   * Create company
   */
  static async createCompany(organizationId: string, data: {
    name: string;
    address?: string;
    email?: string;
    phone?: string;
    website?: string;
    contactPerson?: string;
    userId: string;
  }) {
    const db = await getOrgDb(organizationId);
    return await db.company.create({
      data
    });
  }

  /**
   * Get user SOWs
   */
  static async getUserSOWs(organizationId: string, userId: string, limit?: number) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.findMany({
      where: { userId },
      include: {
        template: true,
        company: true
      },
      orderBy: { createdAt: 'desc' },
      take: limit
    });
  }

  /**
   * Create SOW
   */
  static async createSOW(organizationId: string, data: {
    projectName: string;
    clientName: string;
    vendorName: string;
    scopeOfWork: string;
    timeline: string;
    pricing: string;
    templateId?: string;
    companyId?: string;
    userId: string;
    filePath?: string;
    fileName?: string;
    fileSize?: number;
  }) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.create({
      data,
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Update SOW
   */
  static async updateSOW(organizationId: string, id: string, data: Partial<{
    projectName: string;
    clientName: string;
    vendorName: string;
    scopeOfWork: string;
    timeline: string;
    pricing: string;
    templateId: string;
    companyId: string;
    filePath: string;
    fileName: string;
    fileSize: number;
    status: 'DRAFT' | 'REVIEW' | 'APPROVED' | 'SENT' | 'SIGNED' | 'COMPLETED';
  }>) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.update({
      where: { id },
      data,
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Delete SOW
   */
  static async deleteSOW(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.delete({
      where: {
        id,
        userId // Ensure user owns the SOW
      }
    });
  }

  /**
   * Get SOW by ID
   */
  static async getSOW(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.findFirst({
      where: {
        id,
        userId // Ensure user owns the SOW
      },
      include: {
        template: true,
        company: true
      }
    });
  }

  /**
   * Get template by ID
   */
  static async getTemplate(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.template.findFirst({
      where: {
        id,
        userId // Ensure user owns the template
      }
    });
  }

  /**
   * Delete template
   */
  static async deleteTemplate(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.template.delete({
      where: {
        id,
        userId // Ensure user owns the template
      }
    });
  }

  /**
   * Get user dashboard stats
   */
  static async getUserStats(organizationId: string, userId: string) {
    const db = await getOrgDb(organizationId);
    const [templatesCount, sowsCount, companiesCount, recentSOWs] = await Promise.all([
      db.template.count({ where: { userId } }),
      db.sOW.count({ where: { userId } }),
      db.company.count({ where: { userId } }),
      db.sOW.findMany({
        where: { userId },
        include: { template: true, company: true },
        orderBy: { createdAt: 'desc' },
        take: 5
      })
    ]);

    return {
      templates: templatesCount,
      sows: sowsCount,
      companies: companiesCount,
      recentSOWs
    };
  }

  /**
   * Search SOWs
   */
  static async searchSOWs(organizationId: string, userId: string, query: string) {
    const db = await getOrgDb(organizationId);
    return await db.sOW.findMany({
      where: {
        userId,
        OR: [
          { projectName: { contains: query, mode: 'insensitive' } },
          { clientName: { contains: query, mode: 'insensitive' } },
          { vendorName: { contains: query, mode: 'insensitive' } }
        ]
      },
      include: {
        template: true,
        company: true
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Get company by ID
   */
  static async getCompany(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.company.findFirst({
      where: {
        id,
        userId // Ensure user owns the company
      }
    });
  }

  /**
   * Update company
   */
  static async updateCompany(organizationId: string, id: string, userId: string, data: Partial<{
    name: string;
    address: string;
    email: string;
    phone: string;
    website: string;
    contactPerson: string;
  }>) {
    const db = await getOrgDb(organizationId);
    return await db.company.update({
      where: {
        id,
        userId // Ensure user owns the company
      },
      data
    });
  }

  /**
   * Delete company
   */
  static async deleteCompany(organizationId: string, id: string, userId: string) {
    const db = await getOrgDb(organizationId);
    return await db.company.delete({
      where: {
        id,
        userId // Ensure user owns the company
      }
    });
  }
}
