import { NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  console.log(`🔍 Middleware processing: ${pathname}`);

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // For ALL API requests, provide default user context (development mode)
  if (pathname.startsWith('/api/')) {
    console.log(`🔧 API request detected: ${pathname}`);

    // Always provide development user context
    const response = NextResponse.next();
    response.headers.set('x-user-id', 'dev-user-123');
    response.headers.set('x-user-email', '<EMAIL>');
    response.headers.set('x-organization-id', 'dev-org-123');
    response.headers.set('x-user-role', 'USER');

    console.log(`✅ Development auth applied to: ${pathname}`);
    return response;
  }

  // For non-API routes, just pass through
  console.log(`➡️ Passing through: ${pathname}`);
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
