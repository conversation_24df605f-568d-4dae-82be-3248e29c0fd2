#!/bin/bash

echo "🚀 Starting SOW Generator..."

# Initialize storage directories
echo "📁 Initializing storage directories..."

# Use STORAGE_PATH environment variable or default to /app/storage
STORAGE_ROOT="${STORAGE_PATH:-/app/storage}"
echo "📁 Using storage root: $STORAGE_ROOT"

# Create storage directories
mkdir -p "$STORAGE_ROOT/templates"
mkdir -p "$STORAGE_ROOT/sows"
mkdir -p "$STORAGE_ROOT/uploads"

# Also create local directories for development if not using volume
if [ "$STORAGE_PATH" != "/app/storage" ] || [ "$NODE_ENV" = "development" ]; then
    mkdir -p ./storage/templates
    mkdir -p ./storage/sows
    mkdir -p ./storage/uploads
fi

echo "✅ Storage directories created"

# Try to push database schema (optional for development)
echo "🗄️ Setting up database..."
echo "Database URL: ${DATABASE_URL:0:50}..."

if [ "$NODE_ENV" = "development" ]; then
    echo "🔧 Development mode - database is optional"
    if npx prisma db push --accept-data-loss; then
        echo "✅ Database schema updated successfully"
    else
        echo "⚠️ Database schema push failed in development mode"
        echo "This is OK - Server Actions will work without database"
    fi
else
    # Production mode - database is required
    if npx prisma db push --accept-data-loss; then
        echo "✅ Database schema updated successfully"
    else
        echo "❌ Database schema push failed in production mode"
        echo "This might cause issues with database-dependent features"
    fi
fi

# Generate Prisma client (just in case)
echo "🔧 Generating Prisma client..."
npx prisma generate

# Start the application
echo "🌟 Starting Next.js application..."
exec npm start
