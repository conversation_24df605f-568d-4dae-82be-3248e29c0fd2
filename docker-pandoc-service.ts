import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFile, readFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';

const execAsync = promisify(exec);

/**
 * Docker-based Pandoc Service for Railway Deployment
 * 
 * This service uses Docker to run pandoc in a containerized environment,
 * ensuring consistent document conversion with proper formatting preservation.
 */
export class DockerPandocService {
  private static readonly TEMP_DIR = '/tmp/pandoc-docker';
  private static readonly DOCKER_IMAGE = 'pandoc/latex:latest';

  /**
   * Initialize the service by ensuring temp directory exists
   */
  static async initialize(): Promise<void> {
    try {
      await mkdir(this.TEMP_DIR, { recursive: true });
      console.log('🐳 Docker Pandoc Service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Docker Pandoc Service:', error);
      throw error;
    }
  }

  /**
   * Convert markdown to DOCX using Docker pandoc with reference document
   */
  static async convertMarkdownToDocx(
    markdown: string,
    referenceDocxPath?: string,
    outputFileName: string = 'output.docx'
  ): Promise<Buffer> {
    await this.initialize();

    const timestamp = Date.now();
    const tempMarkdownPath = join(this.TEMP_DIR, `input-${timestamp}.md`);
    const tempOutputPath = join(this.TEMP_DIR, `output-${timestamp}.docx`);
    
    try {
      // Write markdown to temp file
      await writeFile(tempMarkdownPath, markdown, 'utf-8');
      console.log('📝 Markdown written to temp file:', tempMarkdownPath);

      // Build Docker command
      let dockerCommand = `docker run --rm`;
      
      // Mount temp directory
      dockerCommand += ` -v "${this.TEMP_DIR}:/workspace"`;
      
      // Mount reference document if provided
      if (referenceDocxPath) {
        const referenceDir = referenceDocxPath.substring(0, referenceDocxPath.lastIndexOf('/'));
        const referenceFileName = referenceDocxPath.substring(referenceDocxPath.lastIndexOf('/') + 1);
        dockerCommand += ` -v "${referenceDir}:/reference"`;
        dockerCommand += ` ${this.DOCKER_IMAGE}`;
        dockerCommand += ` pandoc "/workspace/input-${timestamp}.md"`;
        dockerCommand += ` -f markdown -t docx`;
        dockerCommand += ` --reference-doc="/reference/${referenceFileName}"`;
        dockerCommand += ` --wrap=preserve --preserve-tabs`;
        dockerCommand += ` -o "/workspace/output-${timestamp}.docx"`;
        
        console.log('🎯 Using reference document for structure preservation');
      } else {
        dockerCommand += ` ${this.DOCKER_IMAGE}`;
        dockerCommand += ` pandoc "/workspace/input-${timestamp}.md"`;
        dockerCommand += ` -f markdown -t docx`;
        dockerCommand += ` -o "/workspace/output-${timestamp}.docx"`;
        
        console.log('📄 Using basic conversion (no reference document)');
      }

      console.log('🐳 Docker Pandoc Command:', dockerCommand);

      // Execute Docker pandoc
      const { stdout, stderr } = await execAsync(dockerCommand);
      
      if (stderr) {
        console.warn('⚠️ Docker Pandoc warnings:', stderr);
      }
      
      if (stdout) {
        console.log('✅ Docker Pandoc output:', stdout);
      }

      // Read the generated DOCX
      const docxBuffer = await readFile(tempOutputPath);
      console.log('📦 Generated DOCX size:', docxBuffer.length, 'bytes');

      return docxBuffer;

    } catch (error) {
      console.error('❌ Docker Pandoc conversion failed:', error);
      throw new Error(`Docker Pandoc conversion failed: ${error}`);
    } finally {
      // Clean up temp files
      try {
        await unlink(tempMarkdownPath);
        await unlink(tempOutputPath);
        console.log('🧹 Temp files cleaned up');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up temp files:', cleanupError);
      }
    }
  }

  /**
   * Check if Docker is available and pandoc image exists
   */
  static async checkDockerAvailability(): Promise<boolean> {
    try {
      // Check if Docker is running
      await execAsync('docker --version');
      console.log('✅ Docker is available');

      // Check if pandoc image is available (pull if not)
      try {
        await execAsync(`docker image inspect ${this.DOCKER_IMAGE}`);
        console.log('✅ Pandoc Docker image is available');
      } catch {
        console.log('📥 Pulling Pandoc Docker image...');
        await execAsync(`docker pull ${this.DOCKER_IMAGE}`);
        console.log('✅ Pandoc Docker image pulled successfully');
      }

      return true;
    } catch (error) {
      console.error('❌ Docker not available:', error);
      return false;
    }
  }

  /**
   * Fallback to native pandoc if Docker is not available
   */
  static async convertWithFallback(
    markdown: string,
    referenceDocxPath?: string,
    outputFileName: string = 'output.docx'
  ): Promise<Buffer> {
    const dockerAvailable = await this.checkDockerAvailability();
    
    if (dockerAvailable) {
      console.log('🐳 Using Docker Pandoc for conversion');
      return this.convertMarkdownToDocx(markdown, referenceDocxPath, outputFileName);
    } else {
      console.log('⚡ Falling back to native pandoc');
      return this.convertWithNativePandoc(markdown, referenceDocxPath, outputFileName);
    }
  }

  /**
   * Native pandoc fallback method
   */
  private static async convertWithNativePandoc(
    markdown: string,
    referenceDocxPath?: string,
    outputFileName: string = 'output.docx'
  ): Promise<Buffer> {
    await this.initialize();

    const timestamp = Date.now();
    const tempMarkdownPath = join(this.TEMP_DIR, `input-${timestamp}.md`);
    const tempOutputPath = join(this.TEMP_DIR, `output-${timestamp}.docx`);
    
    try {
      await writeFile(tempMarkdownPath, markdown, 'utf-8');

      let pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx`;
      
      if (referenceDocxPath) {
        pandocCommand += ` --reference-doc="${referenceDocxPath}" --wrap=preserve --preserve-tabs`;
      }
      
      pandocCommand += ` -o "${tempOutputPath}"`;

      console.log('⚡ Native Pandoc Command:', pandocCommand);
      await execAsync(pandocCommand);

      const docxBuffer = await readFile(tempOutputPath);
      return docxBuffer;

    } finally {
      try {
        await unlink(tempMarkdownPath);
        await unlink(tempOutputPath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temp files:', cleanupError);
      }
    }
  }
}

/**
 * Usage Example:
 * 
 * // With reference document for structure preservation
 * const docxBuffer = await DockerPandocService.convertWithFallback(
 *   markdownContent,
 *   '/path/to/reference.docx',
 *   'output.docx'
 * );
 * 
 * // Basic conversion
 * const docxBuffer = await DockerPandocService.convertWithFallback(
 *   markdownContent
 * );
 */
