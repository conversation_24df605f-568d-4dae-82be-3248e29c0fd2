# Pandoc Docker Containerization Analysis

## Current Issue Analysis

Based on the Railway logs, I can see that:

1. ✅ **Template Upload Works**: Files are being stored in `/app/storage/uploads/`
2. ✅ **Pandoc is Installed**: Pandoc commands are executing successfully
3. ❌ **Reference Document Not Found**: Still getting "Original template not found"

## Root Cause Investigation

The issue might be more complex than just file paths. Let me analyze:

### 1. **File Path Timing Issue**
The logs show:
```
📄 DOCX PREVIEW: Original template not found, using basic conversion
📄 DOCX PREVIEW: Running pandoc command: pandoc "/app/uploads/preview-1750893829605.md" -f markdown -t docx -o "/app/uploads/preview-1750893829605.docx"
```

Notice that it's looking in `/app/uploads/` but the template was stored in `/app/storage/uploads/`. This suggests my fix hasn't been deployed yet.

### 2. **Potential Docker Volume Mount Issue**
Railway uses volume mounts for persistent storage. The issue might be:
- Templates stored in volume: `/app/storage/uploads/`
- Pandoc looking in container: `/app/uploads/`

### 3. **Pandoc Docker Compatibility**
Some pandoc features might not work properly in containerized environments, especially:
- Reference document handling
- File path resolution
- Font and style preservation

## Proposed Solutions

### Solution 1: Verify File Path Fix Deployment
Deploy the current fixes and test if the reference document is now found.

### Solution 2: Docker Pandoc Container
If Railway's pandoc installation has limitations, we could:

```dockerfile
# Add to existing Dockerfile or create pandoc service
FROM pandoc/latex:latest as pandoc-base

# Copy pandoc binary to main container
FROM node:18-alpine
COPY --from=pandoc-base /usr/local/bin/pandoc /usr/local/bin/pandoc
```

### Solution 3: Enhanced Logging & Debugging
Add comprehensive logging to track:
- Exact file paths being checked
- File existence verification
- Pandoc command execution details
- Reference document validation

### Solution 4: Fallback Strategy
Implement a robust fallback system:
1. Try reference document conversion
2. If fails, try basic pandoc conversion
3. If fails, try alternative document generation
4. Log each step for debugging

## Next Steps

1. **Deploy Current Fixes** - Test if path fixes resolve the issue
2. **Add Debug Logging** - Get detailed information about file paths
3. **Test Reference Document** - Verify the template file exists and is accessible
4. **Consider Docker Solution** - If Railway pandoc has limitations

The key is to first verify if our path fixes work, then move to containerization if needed.
